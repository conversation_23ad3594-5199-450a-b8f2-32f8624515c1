{"name": "recall", "private": true, "version": "3.0.0", "type": "module", "description": "AI-powered browser history search and knowledge management extension", "author": "Recall Team", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "dev:mock": "node scripts/dev-server.cjs", "lint": "eslint .", "preview": "vite preview", "test": "jest --config config/jest.config.cjs", "test:unit": "jest --config config/jest.config.cjs --testNamePattern='^((?!integration|performance|error-handling).)*$'", "test:integration": "jest --config config/jest.config.integration.cjs", "test:coverage": "jest --config config/jest.config.cjs --coverage", "test:coverage:analyze": "npm run test:coverage && node scripts/coverage-analysis.cjs", "test:coverage:html": "npm run test:coverage && open coverage/lcov-report/index.html", "test:core": "jest --config config/jest.config.cjs --testNamePattern='(core-business-flows|performance-load|error-handling)'", "test:watch": "jest --config config/jest.config.cjs --watch", "test:e2e": "playwright test --config config/playwright.config.ts", "test:e2e:ui": "playwright test --config config/playwright.config.ts --ui", "test:visual": "node scripts/visual-regression-test.cjs", "test:visual:update": "UPDATE_VISUAL_BASELINE=true node scripts/visual-regression-test.cjs", "test:visual:ui": "VISUAL_HEADLESS=false node scripts/visual-regression-test.cjs", "test:visual:verbose": "VISUAL_VERBOSE=true node scripts/visual-regression-test.cjs", "test:performance": "node scripts/performance-test.cjs", "test:all": "./scripts/test.sh all", "test:clean": "./scripts/test.sh clean", "test:context": "jest --config config/jest.config.cjs tests/contexts/ --testNamePattern='Context'", "test:context:background": "jest --config config/jest.config.cjs tests/contexts/background.context.test.ts", "test:context:content": "jest --config config/jest.config.cjs tests/contexts/content.context.test.ts", "test:context:popup": "jest --config config/jest.config.cjs tests/contexts/popup.context.test.ts", "test:context:options": "jest --config config/jest.config.cjs tests/contexts/options.context.test.ts", "test:integration:ai": "jest --config config/jest.config.cjs tests/integration/ --testNamePattern='integration'", "test:performance:ai": "node scripts/ai-performance-test.cjs", "test:performance:ai:verbose": "PERF_VERBOSE=true node scripts/ai-performance-test.cjs", "test:search:quality": "node scripts/search-quality-test.cjs", "test:search:quality:verbose": "SEARCH_QUALITY_VERBOSE=true node scripts/search-quality-test.cjs", "test:search:quality:baseline": "UPDATE_BASELINE=true node scripts/search-quality-test.cjs", "test:compatibility": "node scripts/compatibility-test.cjs", "test:compatibility:verbose": "COMPAT_VERBOSE=true node scripts/compatibility-test.cjs", "test:compatibility:headed": "COMPAT_HEADLESS=false node scripts/compatibility-test.cjs", "test:security:migration": "node scripts/security-migration-test.cjs", "test:security:migration:verbose": "SECURITY_VERBOSE=true node scripts/security-migration-test.cjs", "test:security:migration:coverage": "SECURITY_COVERAGE=true node scripts/security-migration-test.cjs", "test:security:pentest": "jest --config config/jest.config.cjs tests/security/penetration.security.test.ts", "test:migration:integrity": "jest --config config/jest.config.cjs tests/storage/migration-security.integration.test.ts", "test:security:full": "node scripts/security-migration-test.cjs && npm run test:security:pentest", "test:extension:errors": "node scripts/extension-error-monitor.cjs", "test:extension:errors:watch": "node scripts/extension-error-monitor.cjs --watch", "test:extension:errors:headless": "node scripts/extension-error-monitor.cjs --headless", "test:extension:errors:quick": "node scripts/extension-error-monitor.cjs --max-cycles=10", "test:extension:errors:verbose": "node scripts/extension-error-monitor.cjs --verbose", "test:extension:errors:quiet": "node scripts/extension-error-monitor.cjs --quiet", "check:extension:health": "node scripts/extension-error-monitor.cjs --max-cycles=1 --headless", "check:architecture": "node scripts/architecture-check.cjs", "check:architecture:background": "node scripts/architecture-check.cjs src/background/", "check:architecture:content": "node scripts/architecture-check.cjs src/content/", "check:architecture:popup": "node scripts/architecture-check.cjs src/popup/", "check:architecture:all": "node scripts/architecture-check.cjs src/", "type-check": "tsc --noEmit", "lint:ci": "eslint src/ --ext .ts,.tsx --format json --output-file test-results/lint-results.json", "hooks:install": "./scripts/setup-hooks.sh", "hooks:update": "./scripts/setup-hooks.sh --update", "hooks:run": "pre-commit run --all-files", "prepare": "husky install || npm run hooks:install || true"}, "dependencies": {"@mozilla/readability": "^0.6.0", "fuse.js": "^7.1.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "jieba-wasm": "^2.2.0", "lunr": "^2.3.9", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.3", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "zustand": "^5.0.6"}, "devDependencies": {"@crxjs/vite-plugin": "^2.0.2", "@eslint/js": "^9.25.0", "@playwright/test": "^1.53.0", "@testing-library/react": "^16.3.0", "@types/chrome": "^0.0.326", "@types/jest": "^30.0.0", "@types/lunr": "^2.3.7", "@types/node": "^24.0.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.4.1", "cors": "^2.8.5", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "express": "^4.19.2", "globals": "^16.0.0", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "jest-html-reporter": "^4.3.0", "jest-junit": "^16.0.0", "ts-jest": "^29.4.0", "tsx": "^4.20.3", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-top-level-await": "^1.5.0", "vite-plugin-wasm": "^3.4.1"}}