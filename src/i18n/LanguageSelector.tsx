/**
 * IMPL-404-001: LanguageSelector Component - Multi-language Support UI
 * 
 * React component that provides:
 * - Language selection dropdown in top-right position
 * - Real-time language switching without page refresh
 * - Performance optimized switching (<50ms)
 * - Accessibility support with ARIA labels
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { I18nManager } from './I18nManager';
import type { SupportedLanguage } from './I18nManager';
import { useI18nState } from '../stores';
import './LanguageSelector.css';

export interface LanguageSelectorProps {
  onLanguageChange?: (language: SupportedLanguage) => void;
  className?: string;
  disabled?: boolean;
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  onLanguageChange,
  className = '',
  disabled = false
}) => {
  // Use Zustand store for language state
  const {
    currentLanguage,
    isLanguageChanging,
    i18nReady
  } = useI18nState();
  
  const [isOpen, setIsOpen] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const i18nManager = useRef(I18nManager.getInstance());
  const selectorRef = useRef<HTMLDivElement>(null);

  // Stable reference for the language change callback to prevent infinite re-renders
  const onLanguageChangeRef = useRef(onLanguageChange);

  // Update the ref when the prop changes, but don't trigger re-renders
  useEffect(() => {
    onLanguageChangeRef.current = onLanguageChange;
  }, [onLanguageChange]);

  // Initialize based on i18n ready state from store
  useEffect(() => {
    if (i18nReady && !isInitialized) {
      setIsInitialized(true);
    }
  }, [i18nReady]); // Remove isInitialized from dependency to prevent loop

  // Language change is now handled directly through I18nManager
  // App.tsx will pick up the change through its own listener and update Zustand store

  // Remove duplicate language change listener - App.tsx already handles this
  // The language state updates will be managed by App.tsx through Zustand store
  // This prevents duplicate listeners that can cause nested update errors

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectorRef.current && !selectorRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle language selection - simplified to avoid nested updates
  const selectLanguage = useCallback(async (language: SupportedLanguage) => {
    // Prevent multiple simultaneous language changes
    if (disabled || language === currentLanguage || isLanguageChanging) {
      setIsOpen(false);
      return;
    }

    setIsOpen(false);
    
    try {
      // Just call I18nManager - App.tsx will handle the state updates
      await i18nManager.current.setLanguage(language);
      
      // Call the optional callback if provided
      if (onLanguageChangeRef.current) {
        await onLanguageChangeRef.current(language);
      }
    } catch (error) {
      console.error('Failed to switch language:', error);
    }
  }, [disabled, currentLanguage, isLanguageChanging]);

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        setIsOpen(!isOpen);
        break;
      case 'Escape':
        setIsOpen(false);
        break;
      case 'ArrowDown':
        event.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        }
        break;
      case 'ArrowUp':
        event.preventDefault();
        if (isOpen) {
          setIsOpen(false);
        }
        break;
    }
  };

  const supportedLanguages = i18nManager.current.getSupportedLanguages();
  const currentLanguageDisplay = i18nManager.current.getLanguageDisplayName(currentLanguage);

  return (
    <div 
      ref={selectorRef}
      className={`language-selector ${className} ${isOpen ? 'language-selector--open' : ''}`}
      data-testid="language-selector"
    >
      <button
        className={`language-selector__trigger ${disabled ? 'language-selector__trigger--disabled' : ''}`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        onKeyDown={handleKeyDown}
        disabled={disabled || isLanguageChanging || !isInitialized}
        aria-label={isInitialized ? i18nManager.current.getTranslation('common.language') : 'Loading languages'}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        role="combobox"
        data-testid="language-trigger"
      >
        <span className="language-selector__current">
          {isInitialized ? currentLanguageDisplay : '...'}
        </span>
        <span 
          className={`language-selector__arrow ${isOpen ? 'language-selector__arrow--up' : ''}`}
          aria-hidden="true"
        >
          ▼
        </span>
      </button>

      {isOpen && isInitialized && (
        <div 
          className="language-selector__dropdown"
          role="listbox"
          aria-label="Language options"
        >
          {supportedLanguages.map((language) => {
            const displayName = i18nManager.current.getLanguageDisplayName(language);
            const isSelected = language === currentLanguage;
            
            return (
              <button
                key={language}
                className={`language-selector__option ${isSelected ? 'language-selector__option--selected' : ''}`}
                onClick={() => selectLanguage(language)}
                role="option"
                aria-selected={isSelected}
                data-testid={`language-${language}`}
                disabled={isLanguageChanging}
              >
                <span className="language-selector__option-text">
                  {displayName}
                </span>
                {isSelected && (
                  <span className="language-selector__check" aria-hidden="true">
                    ✓
                  </span>
                )}
              </button>
            );
          })}
        </div>
      )}

      {(isLanguageChanging || !isInitialized) && (
        <div className="language-selector__loading" role="status" aria-live="polite">
          <span className="language-selector__spinner" aria-hidden="true"></span>
          <span className="sr-only">
            {isInitialized ? 'Switching language...' : 'Loading languages...'}
          </span>
        </div>
      )}
    </div>
  );
};