/**
 * History Management Component
 * 
 * Provides a virtual scrolling interface for managing browsing history
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
// @ts-ignore - No types available for react-window-infinite-loader
import InfiniteLoader from 'react-window-infinite-loader';
import { dbService, hybridSearchService } from '../../services';
import { parseQuery } from '../../services/query-parser';
import { Highlight } from '../../components/Highlight';
import type { Page, PaginationOptions, SearchOptions } from '../../models';
import { formatTimestamp } from '../../models';
import { I18nManager } from '../../i18n/I18nManager';
import './HistoryManagement.css';

/**
 * History management props
 */
interface HistoryManagementProps {
  className?: string;
}

/**
 * History item props for virtual list
 */
interface HistoryItemProps {
  index: number;
  style: React.CSSProperties;
  data: {
    items: Page[];
    searchKeywords: string[];
    onItemClick: (page: Page) => void;
    onItemDelete: (page: Page) => void;
    t: (key: string, params?: Record<string, any>) => string;
  };
}

/**
 * History statistics interface
 */
interface HistoryStats {
  totalPages: number;
  totalDomains: number;
  oldestPage: number;
  newestPage: number;
  totalSize: number;
}

/**
 * Individual history item component
 */
const HistoryItem: React.FC<HistoryItemProps> = ({ index, style, data }) => {
  const { items, searchKeywords, onItemClick, onItemDelete, t } = data;
  const page = items[index];

  if (!page) {
    return (
      <div style={style} className="history-item loading">
        <div className="loading-placeholder">
          <div className="loading-line title"></div>
          <div className="loading-line url"></div>
          <div className="loading-line content"></div>
        </div>
      </div>
    );
  }

  const handleClick = () => onItemClick(page);
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onItemDelete(page);
  };

  return (
    <div style={style} className="history-item" onClick={handleClick}>
      <div className="history-item-favicon">
        <img 
          src={`https://www.google.com/s2/favicons?domain=${page.domain}&sz=32`} 
          alt="" 
          onError={(e) => { e.currentTarget.style.display = 'none'; }}
        />
      </div>
      <div className="history-item-main">
        <div className="history-item-title">
          <Highlight
            text={page.title}
            keywords={searchKeywords}
            className="highlight-title"
            maxLength={100}
          />
        </div>
        <div className="history-item-url">
          <Highlight
            text={page.url}
            keywords={searchKeywords}
            className="highlight-url"
            maxLength={120}
          />
        </div>
        <div className="history-item-content">
          <Highlight
            text={page.content}
            keywords={searchKeywords}
            className="highlight-content"
            maxLength={200}
          />
        </div>
      </div>
      <div className="history-item-aside">
        <div className="history-meta">
          <span className="meta-item" title={t('options.historyManagement.item.accessCountTooltip')}>
            👁️ {page.accessCount}
          </span>
          <span className="meta-item" title={t('options.historyManagement.item.lastVisitTooltip')}>
            📅 {formatTimestamp(page.visitTime)}
          </span>
          <span className="meta-item" title={t('options.historyManagement.item.contentSizeTooltip')}>
            📄 {t('options.historyManagement.item.sizeKB', { size: Math.round(page.content.length / 1024) })}
          </span>
        </div>
        <div className="history-actions">
          <button
            className="delete-btn"
            onClick={handleDelete}
            title={t('options.historyManagement.item.delete')}
            aria-label={t('options.historyManagement.item.deleteAriaLabel', { title: page.title })}
          >
            🗑️
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * History Management Component
 */
export const HistoryManagement: React.FC<HistoryManagementProps> = ({ className = '' }) => {
  // I18n setup
  const [i18nManager] = useState(() => I18nManager.getInstance());
  const [, forceUpdate] = useState({});

  // Translation helper function
  const t = useCallback((key: string, params?: Record<string, any>) => {
    return i18nManager.getTranslation(key, params);
  }, [i18nManager]);

  const [pages, setPages] = useState<Page[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'visitTime' | 'lastUpdated' | 'domain' | 'id'>('visitTime');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [stats, setStats] = useState<HistoryStats | null>(null);
  const [hasNextPage, setHasNextPage] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Pagination state
  const [cursor, setCursor] = useState<string | null>(null);
  const pageSize = 50;

  // Language change handler to force re-render
  useEffect(() => {
    const handleLanguageChange = () => {
      forceUpdate({});
    };

    i18nManager.addLanguageChangeListener(handleLanguageChange);
    return () => {
      i18nManager.removeLanguageChangeListener(handleLanguageChange);
    };
  }, [i18nManager]);

  // Load current language resources on mount
  useEffect(() => {
    i18nManager.loadLanguageResources(i18nManager.getCurrentLanguage()).catch(console.error);
  }, [i18nManager]);

  /**
   * Parse search keywords for highlighting
   */
  const searchKeywords = useMemo(() => {
    if (!searchQuery) return [];
    const parsed = parseQuery(searchQuery);
    return [...parsed.keywords, ...parsed.exact];
  }, [searchQuery]);

  /**
   * Load history statistics
   */
  const loadStats = useCallback(async () => {
    try {
      const totalPages = await dbService.getPageCount();
      const allPages = await dbService.getAllPages();
      
      if (allPages.length === 0) {
        setStats({
          totalPages: 0,
          totalDomains: 0,
          oldestPage: 0,
          newestPage: 0,
          totalSize: 0
        });
        return;
      }

      const domains = new Set(allPages.map(p => p.domain));
      const visitTimes = allPages.map(p => p.visitTime);
      const totalSize = allPages.reduce((sum, p) => sum + p.content.length, 0);

      setStats({
        totalPages,
        totalDomains: domains.size,
        oldestPage: Math.min(...visitTimes),
        newestPage: Math.max(...visitTimes),
        totalSize
      });
    } catch (error) {
      console.error('Failed to load history stats:', error);
    }
  }, []);

  /**
   * Load initial history data
   */
  const loadHistory = useCallback(async (reset = false) => {
    try {
      setLoading(true);
      
      let results: Page[];
      
      if (searchQuery.trim()) {
        // Use hybrid search service for filtered results (same as popup)
        const searchOptions: Partial<SearchOptions> = { limit: 50 };
        const searchResults = await hybridSearchService.search(searchQuery, searchOptions);
        results = searchResults.map(r => r.page);
        setHasNextPage(false); // Search results are not paginated
      } else {
        // Use pagination for all results
        const paginationOptions: PaginationOptions = {
          limit: pageSize,
          cursor: reset ? null : cursor,
          sortBy,
          sortOrder
        };
        
        const paginatedResult = await dbService.getPagesPaginated(paginationOptions);
        results = paginatedResult.items;
        setHasNextPage(paginatedResult.hasMore);
        setCursor(paginatedResult.nextCursor);
      }

      if (reset) {
        setPages(results);
      } else {
        setPages(prev => [...prev, ...results]);
      }
    } catch (error) {
      console.error('Failed to load history:', error);
    } finally {
      setLoading(false);
      setIsLoadingMore(false);
    }
  }, [searchQuery, sortBy, sortOrder, cursor, pageSize]);

  /**
   * Load more items for infinite scrolling
   */
  const loadMore = useCallback(async () => {
    if (isLoadingMore || !hasNextPage || searchQuery.trim()) return;
    
    setIsLoadingMore(true);
    await loadHistory(false);
  }, [isLoadingMore, hasNextPage, searchQuery, loadHistory]);

  /**
   * Handle search input change
   */
  const handleSearchChange = useCallback((value: string) => {
    setSearchQuery(value);
    setCursor(null);
  }, []);

  /**
   * Handle sort change
   */
  const handleSortChange = useCallback((newSortBy: typeof sortBy, newSortOrder: typeof sortOrder) => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    setCursor(null);
  }, []);

  /**
   * Handle item click - open in new tab
   */
  const handleItemClick = useCallback((page: Page) => {
    chrome.tabs.create({ url: page.url });
  }, []);

  /**
   * Handle item deletion
   */
  const handleItemDelete = useCallback(async (page: Page) => {
    if (!confirm(t('options.historyManagement.actions.deleteConfirm', { title: page.title }))) return;
    
    try {
      await dbService.deletePage(page.id);
      setPages(prev => prev.filter(p => p.id !== page.id));
      await loadStats(); // Refresh stats
    } catch (error) {
      console.error('Failed to delete page:', error);
      alert(t('options.historyManagement.actions.deleteError'));
    }
  }, [loadStats, t]);

  /**
   * Check if item is loaded for infinite loader
   */
  const isItemLoaded = useCallback((index: number) => {
    return !!pages[index];
  }, [pages]);

  // Load initial data
  useEffect(() => {
    loadHistory(true);
    loadStats();
  }, [searchQuery, sortBy, sortOrder]);

  // Prepare data for virtual list
  const listData = useMemo(() => ({
    items: pages,
    searchKeywords,
    onItemClick: handleItemClick,
    onItemDelete: handleItemDelete,
    t
  }), [pages, searchKeywords, handleItemClick, handleItemDelete, t]);

  const itemCount = hasNextPage ? pages.length + 1 : pages.length;

  return (
    <div className={`history-management ${className}`}>
       {/* Header with search and controls */}
       <div className="history-header">
         <div className="history-title-section">
           <h2>{t('options.historyManagement.title')}</h2>
           {stats && (
             <div className="history-stats">
               <span>{t('options.historyManagement.stats.totalPages', { count: stats.totalPages })}</span>
               <span>{t('options.historyManagement.stats.totalDomains', { count: stats.totalDomains })}</span>
               <span>{t('options.historyManagement.stats.totalSize', { size: Math.round(stats.totalSize / 1024 / 1024) })}</span>
             </div>
           )}
         </div>
         
         <div className="history-controls">
           <div className="search-section">
             <input
               type="text"
               value={searchQuery}
               onChange={(e) => handleSearchChange(e.target.value)}
               placeholder={t('options.historyManagement.search.placeholder')}
               className="history-search"
             />
           </div>
           
           <div className="sort-section">
             <label htmlFor="sort-select" className="sort-label">{t('options.historyManagement.sorting.label')}</label>
             <select
               id="sort-select"
               value={`${sortBy}-${sortOrder}`}
               onChange={(e) => {
                 const [newSortBy, newSortOrder] = e.target.value.split('-') as [typeof sortBy, typeof sortOrder];
                 handleSortChange(newSortBy, newSortOrder);
               }}
               className="sort-select"
             >
               <option value="visitTime-desc">{t('options.historyManagement.sorting.options.visitTimeDesc')}</option>
               <option value="visitTime-asc">{t('options.historyManagement.sorting.options.visitTimeAsc')}</option>
               <option value="lastUpdated-desc">{t('options.historyManagement.sorting.options.lastUpdatedDesc')}</option>
               <option value="lastUpdated-asc">{t('options.historyManagement.sorting.options.lastUpdatedAsc')}</option>
               <option value="domain-asc">{t('options.historyManagement.sorting.options.domainAsc')}</option>
               <option value="id-desc">{t('options.historyManagement.sorting.options.idDesc')}</option>
             </select>
           </div>
         </div>
       </div>

      {/* Virtualized List */}
      <div className="history-list-container">
        {loading ? (
          <div className="loading-state">
            <div className="loading-spinner-large" />
            <p>{t('options.historyManagement.loading')}</p>
          </div>
        ) : pages.length === 0 ? (
          <div className="empty-state">
            <span className="empty-icon">📭</span>
            <h3>{t('options.historyManagement.emptyState.noHistory')}</h3>
            {searchQuery ? (
              <p>{t('options.historyManagement.emptyState.noSearchResults', { query: searchQuery })}</p>
            ) : (
              <p>{t('options.historyManagement.emptyState.startBrowsing')}</p>
            )}
          </div>
        ) : (
          <InfiniteLoader
            isItemLoaded={isItemLoaded}
            itemCount={itemCount}
            loadMoreItems={loadMore}
          >
            {({ onItemsRendered, ref }: any) => (
              <List
                ref={ref}
                height={600}
                width="100%"
                itemCount={itemCount}
                itemSize={140}
                itemData={listData}
                onItemsRendered={onItemsRendered}
                className="history-virtual-list"
              >
                {HistoryItem}
              </List>
            )}
          </InfiniteLoader>
        )}
      </div>
    </div>
  );
};
