/**
 * Tests for the main application Zustand store
 */

import { act, renderHook } from '@testing-library/react';
import { useAppStore } from './appStore';

// Mock Chrome API
global.chrome = {
  storage: {
    local: {
      get: jest.fn().mockResolvedValue({}),
      set: jest.fn().mockResolvedValue(undefined),
    }
  }
} as any;

describe('AppStore', () => {
  beforeEach(() => {
    // Reset store to initial state
    useAppStore.setState({
      searchQuery: '',
      searchResults: [],
      totalResults: 0,
      searchTime: 0,
      isLoading: false,
      error: null,
      filters: {},
      densityMode: 'comfortable',
      isInitialized: false,
      i18nReady: false,
    });
    jest.clearAllMocks();
  });

  describe('Search functionality', () => {
    it('should update search query', () => {
      const { result } = renderHook(() => useAppStore());
      
      act(() => {
        result.current.setSearchQuery('test query');
      });

      expect(result.current.searchQuery).toBe('test query');
    });

    it('should update search results', () => {
      const { result } = renderHook(() => useAppStore());
      const mockResults = [
        { id: '1', title: 'Test Result', content: 'Content', url: 'https://test.com', timestamp: Date.now(), score: 0.9 }
      ];

      act(() => {
        result.current.setSearchResults(mockResults);
      });

      expect(result.current.searchResults).toEqual(mockResults);
      expect(result.current.totalResults).toBe(1);
    });

    it('should handle loading states', () => {
      const { result } = renderHook(() => useAppStore());

      act(() => {
        result.current.setLoading(true);
      });

      expect(result.current.isLoading).toBe(true);

      act(() => {
        result.current.setLoading(false);
      });

      expect(result.current.isLoading).toBe(false);
    });

    it('should update search stats', () => {
      const { result } = renderHook(() => useAppStore());

      act(() => {
        result.current.setSearchStats(100, 150);
      });

      expect(result.current.totalResults).toBe(100);
      expect(result.current.searchTime).toBe(150);
    });

    it('should clear search', () => {
      const { result } = renderHook(() => useAppStore());

      // Set some data first
      act(() => {
        result.current.setSearchQuery('test');
        result.current.setSearchResults([
          { id: '1', title: 'Test', content: 'Content', url: 'https://test.com', timestamp: Date.now(), score: 0.9 }
        ]);
        result.current.setSearchStats(10, 200);
      });

      // Clear search
      act(() => {
        result.current.clearSearch();
      });

      expect(result.current.searchQuery).toBe('');
      expect(result.current.searchResults).toHaveLength(0);
      expect(result.current.totalResults).toBe(0);
      expect(result.current.searchTime).toBe(0);
    });
  });

  describe('Error handling', () => {
    it('should set and clear errors', () => {
      const { result } = renderHook(() => useAppStore());
      const testError = {
        type: 'search' as const,
        title: 'Search Error',
        message: 'Test error message',
        solutions: ['Try again'],
        canRetry: true,
      };

      act(() => {
        result.current.setError(testError);
      });

      expect(result.current.error).toEqual(testError);

      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
    });
  });

  describe('Filters management', () => {
    it('should set filters', () => {
      const { result } = renderHook(() => useAppStore());
      const filters = { maxResults: 50, enableFuzzySearch: true };

      act(() => {
        result.current.setFilters(filters);
      });

      expect(result.current.filters).toEqual(filters);
    });

    it('should update filters partially', () => {
      const { result } = renderHook(() => useAppStore());

      // Set initial filters
      act(() => {
        result.current.setFilters({ maxResults: 50, enableFuzzySearch: true });
      });

      // Update partially
      act(() => {
        result.current.updateFilters({ maxResults: 100 });
      });

      expect(result.current.filters.maxResults).toBe(100);
      expect(result.current.filters.enableFuzzySearch).toBe(true);
    });

    it('should clear filters', () => {
      const { result } = renderHook(() => useAppStore());

      // Set filters first
      act(() => {
        result.current.setFilters({ maxResults: 50 });
      });

      // Clear filters
      act(() => {
        result.current.clearFilters();
      });

      expect(result.current.filters).toEqual({});
    });
  });

  describe('UI state management', () => {
    it('should update density mode', () => {
      const { result } = renderHook(() => useAppStore());

      act(() => {
        result.current.setDensityMode('compact');
      });

      expect(result.current.densityMode).toBe('compact');
    });

    it('should toggle density mode', () => {
      const { result } = renderHook(() => useAppStore());

      // Start with comfortable
      expect(result.current.densityMode).toBe('comfortable');

      act(() => {
        result.current.toggleDensityMode();
      });

      expect(result.current.densityMode).toBe('compact');

      act(() => {
        result.current.toggleDensityMode();
      });

      expect(result.current.densityMode).toBe('comfortable');
    });

    it('should update initialization state', () => {
      const { result } = renderHook(() => useAppStore());

      act(() => {
        result.current.setInitialized(true);
      });

      expect(result.current.isInitialized).toBe(true);
    });

    it('should update i18n ready state', () => {
      const { result } = renderHook(() => useAppStore());

      act(() => {
        result.current.setI18nReady(true);
      });

      expect(result.current.i18nReady).toBe(true);
    });
  });

  describe('Batch updates', () => {
    it('should handle batch updates', () => {
      const { result } = renderHook(() => useAppStore());

      act(() => {
        result.current.batchUpdate({
          searchQuery: 'batch test',
          isLoading: true,
          totalResults: 5,
        });
      });

      expect(result.current.searchQuery).toBe('batch test');
      expect(result.current.isLoading).toBe(true);
      expect(result.current.totalResults).toBe(5);
    });
  });
});