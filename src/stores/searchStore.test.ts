/**
 * Tests for the search Zustand store
 */

import { act, renderHook } from '@testing-library/react';
import { 
  useSearchStore,
  useSuggestionsState,
  useHistoryState,
  usePerformanceState,
  useSearchConfig,
  generateSearchId,
  formatPerformanceStats
} from './searchStore';
import type { SearchSuggestion, SearchHistory, SearchPerformanceStats } from './searchStore';

describe('SearchStore', () => {
  beforeEach(() => {
    // Reset store to initial state
    useSearchStore.setState({
      suggestions: [],
      showSuggestions: false,
      selectedSuggestionIndex: -1,
      isLoadingSuggestions: false,
      searchHistory: [],
      performanceStats: {
        averageSearchTime: 0,
        totalSearches: 0,
        failedSearches: 0,
        lastUpdated: Date.now(),
      },
      config: {
        enableSuggestions: true,
        maxSuggestions: 5,
        suggestionDelay: 300,
        enableHistory: true,
        maxHistoryItems: 100,
        autoSearch: true,
        debounceDelay: 300,
        enablePerformanceTracking: true,
        maxResultsPerPage: 50,
        enablePagination: false,
      },
      currentSearchId: null,
      searchStartTime: null,
    });
    jest.clearAllMocks();
  });

  describe('Suggestions functionality', () => {
    it('should set suggestions with limit', () => {
      const { result } = renderHook(() => useSearchStore());
      const suggestions: SearchSuggestion[] = Array.from({ length: 10 }, (_, i) => ({
        query: `suggestion ${i}`,
        frequency: 1,
        lastUsed: Date.now(),
        category: 'recent' as const,
      }));

      act(() => {
        result.current.setSuggestions(suggestions);
      });

      // Should be limited to maxSuggestions (5)
      expect(result.current.suggestions).toHaveLength(5);
      expect(result.current.selectedSuggestionIndex).toBe(-1);
    });

    it('should add new suggestion', () => {
      const { result } = renderHook(() => useSearchStore());
      const suggestion: SearchSuggestion = {
        query: 'new suggestion',
        frequency: 1,
        lastUsed: Date.now(),
        category: 'recent',
      };

      act(() => {
        result.current.addSuggestion(suggestion);
      });

      expect(result.current.suggestions).toHaveLength(1);
      expect(result.current.suggestions[0].query).toBe('new suggestion');
    });

    it('should update existing suggestion frequency', () => {
      const { result } = renderHook(() => useSearchStore());
      const suggestion: SearchSuggestion = {
        query: 'existing suggestion',
        frequency: 1,
        lastUsed: Date.now(),
        category: 'recent',
      };

      act(() => {
        result.current.addSuggestion(suggestion);
      });

      expect(result.current.suggestions[0].frequency).toBe(1);

      act(() => {
        result.current.addSuggestion(suggestion);
      });

      expect(result.current.suggestions[0].frequency).toBe(2);
    });

    it('should clear suggestions', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.setSuggestions([{
          query: 'test',
          frequency: 1,
          lastUsed: Date.now(),
        }]);
        result.current.setShowSuggestions(true);
      });

      act(() => {
        result.current.clearSuggestions();
      });

      expect(result.current.suggestions).toHaveLength(0);
      expect(result.current.showSuggestions).toBe(false);
      expect(result.current.selectedSuggestionIndex).toBe(-1);
    });

    it('should handle suggestion visibility', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.setShowSuggestions(true);
      });

      expect(result.current.showSuggestions).toBe(true);

      act(() => {
        result.current.setShowSuggestions(false);
      });

      expect(result.current.showSuggestions).toBe(false);
    });

    it('should update selected suggestion index', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.setSelectedSuggestionIndex(2);
      });

      expect(result.current.selectedSuggestionIndex).toBe(2);
    });

    it('should handle loading state', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.setLoadingSuggestions(true);
      });

      expect(result.current.isLoadingSuggestions).toBe(true);

      act(() => {
        result.current.setLoadingSuggestions(false);
      });

      expect(result.current.isLoadingSuggestions).toBe(false);
    });
  });

  describe('Search history functionality', () => {
    it('should add to history', () => {
      const { result } = renderHook(() => useSearchStore());
      const historyItem: SearchHistory = {
        query: 'test query',
        timestamp: Date.now(),
        resultsCount: 10,
        searchTime: 150,
      };

      act(() => {
        result.current.addToHistory(historyItem);
      });

      expect(result.current.searchHistory).toHaveLength(1);
      expect(result.current.searchHistory[0].query).toBe('test query');
    });

    it('should not add to history when disabled', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.updateConfig({ enableHistory: false });
      });

      const historyItem: SearchHistory = {
        query: 'test query',
        timestamp: Date.now(),
        resultsCount: 10,
        searchTime: 150,
      };

      act(() => {
        result.current.addToHistory(historyItem);
      });

      expect(result.current.searchHistory).toHaveLength(0);
    });

    it('should remove duplicate queries', () => {
      const { result } = renderHook(() => useSearchStore());
      const historyItem1: SearchHistory = {
        query: 'same query',
        timestamp: Date.now() - 1000,
        resultsCount: 5,
        searchTime: 100,
      };
      const historyItem2: SearchHistory = {
        query: 'same query',
        timestamp: Date.now(),
        resultsCount: 10,
        searchTime: 150,
      };

      act(() => {
        result.current.addToHistory(historyItem1);
        result.current.addToHistory(historyItem2);
      });

      expect(result.current.searchHistory).toHaveLength(1);
      expect(result.current.searchHistory[0].timestamp).toBe(historyItem2.timestamp);
    });

    it('should limit history size', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.updateConfig({ maxHistoryItems: 3 });
      });

      for (let i = 0; i < 5; i++) {
        act(() => {
          result.current.addToHistory({
            query: `query ${i}`,
            timestamp: Date.now() + i,
            resultsCount: 10,
            searchTime: 150,
          });
        });
      }

      expect(result.current.searchHistory).toHaveLength(3);
    });

    it('should clear history', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.addToHistory({
          query: 'test',
          timestamp: Date.now(),
          resultsCount: 10,
          searchTime: 150,
        });
      });

      act(() => {
        result.current.clearHistory();
      });

      expect(result.current.searchHistory).toHaveLength(0);
    });

    it('should remove specific history item', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.addToHistory({
          query: 'keep this',
          timestamp: Date.now(),
          resultsCount: 10,
          searchTime: 150,
        });
        result.current.addToHistory({
          query: 'remove this',
          timestamp: Date.now() + 1,
          resultsCount: 5,
          searchTime: 100,
        });
      });

      act(() => {
        result.current.removeFromHistory('remove this');
      });

      expect(result.current.searchHistory).toHaveLength(1);
      expect(result.current.searchHistory[0].query).toBe('keep this');
    });
  });

  describe('Performance tracking', () => {
    it('should start and end search tracking', () => {
      const { result } = renderHook(() => useSearchStore());
      const searchId = 'test-search-123';

      act(() => {
        result.current.startSearch(searchId);
      });

      expect(result.current.currentSearchId).toBe(searchId);
      expect(result.current.searchStartTime).toBeGreaterThan(0);

      // Wait a bit and end search
      setTimeout(() => {
        act(() => {
          result.current.endSearch(searchId, 10, true);
        });

        expect(result.current.currentSearchId).toBeNull();
        expect(result.current.searchStartTime).toBeNull();
        expect(result.current.performanceStats.totalSearches).toBe(1);
        expect(result.current.performanceStats.failedSearches).toBe(0);
      }, 10);
    });

    it('should track failed searches', () => {
      const { result } = renderHook(() => useSearchStore());
      const searchId = 'test-search-456';

      act(() => {
        result.current.startSearch(searchId);
      });

      setTimeout(() => {
        act(() => {
          result.current.endSearch(searchId, 0, false);
        });

        expect(result.current.performanceStats.totalSearches).toBe(1);
        expect(result.current.performanceStats.failedSearches).toBe(1);
      }, 10);
    });

    it('should not track when disabled', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.updateConfig({ enablePerformanceTracking: false });
      });

      const searchId = 'test-search-789';

      act(() => {
        result.current.startSearch(searchId);
        result.current.endSearch(searchId, 10, true);
      });

      expect(result.current.performanceStats.totalSearches).toBe(0);
    });

    it('should update performance stats manually', () => {
      const { result } = renderHook(() => useSearchStore());
      const newStats: Partial<SearchPerformanceStats> = {
        averageSearchTime: 200,
        totalSearches: 50,
      };

      act(() => {
        result.current.updatePerformanceStats(newStats);
      });

      expect(result.current.performanceStats.averageSearchTime).toBe(200);
      expect(result.current.performanceStats.totalSearches).toBe(50);
      expect(result.current.performanceStats.lastUpdated).toBeGreaterThan(0);
    });
  });

  describe('Configuration management', () => {
    it('should update config partially', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.updateConfig({
          maxSuggestions: 10,
          enableFuzzySearch: false,
        });
      });

      expect(result.current.config.maxSuggestions).toBe(10);
      expect(result.current.config.enableSuggestions).toBe(true); // Should remain unchanged
    });

    it('should reset config to defaults', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.updateConfig({
          maxSuggestions: 20,
          enableSuggestions: false,
        });
      });

      act(() => {
        result.current.resetConfig();
      });

      expect(result.current.config.maxSuggestions).toBe(5); // Default value
      expect(result.current.config.enableSuggestions).toBe(true); // Default value
    });
  });

  describe('Data management', () => {
    it('should export search data', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.addSuggestion({
          query: 'test suggestion',
          frequency: 1,
          lastUsed: Date.now(),
        });
        result.current.addToHistory({
          query: 'test query',
          timestamp: Date.now(),
          resultsCount: 10,
          searchTime: 150,
        });
      });

      const exported = result.current.exportSearchData();

      expect(exported.suggestions).toHaveLength(1);
      expect(exported.history).toHaveLength(1);
      expect(exported.stats).toBeDefined();
    });

    it('should import search data', () => {
      const { result } = renderHook(() => useSearchStore());
      const importData = {
        suggestions: [{
          query: 'imported suggestion',
          frequency: 5,
          lastUsed: Date.now(),
        }],
        history: [{
          query: 'imported query',
          timestamp: Date.now(),
          resultsCount: 15,
          searchTime: 200,
        }],
        stats: {
          averageSearchTime: 180,
          totalSearches: 20,
          failedSearches: 2,
          lastUpdated: Date.now(),
        },
      };

      act(() => {
        result.current.importSearchData(importData);
      });

      expect(result.current.suggestions).toHaveLength(1);
      expect(result.current.searchHistory).toHaveLength(1);
      expect(result.current.performanceStats.totalSearches).toBe(20);
    });

    it('should clear all data', () => {
      const { result } = renderHook(() => useSearchStore());

      act(() => {
        result.current.addSuggestion({
          query: 'test',
          frequency: 1,
          lastUsed: Date.now(),
        });
        result.current.addToHistory({
          query: 'test',
          timestamp: Date.now(),
          resultsCount: 10,
          searchTime: 150,
        });
        result.current.setShowSuggestions(true);
      });

      act(() => {
        result.current.clearAllData();
      });

      expect(result.current.suggestions).toHaveLength(0);
      expect(result.current.searchHistory).toHaveLength(0);
      expect(result.current.showSuggestions).toBe(false);
      expect(result.current.selectedSuggestionIndex).toBe(-1);
      expect(result.current.performanceStats.totalSearches).toBe(0);
    });
  });

  describe('Selector hooks', () => {
    it('should provide suggestions state', () => {
      const { result } = renderHook(() => useSuggestionsState());

      expect(result.current.suggestions).toBeDefined();
      expect(result.current.setSuggestions).toBeInstanceOf(Function);
      expect(result.current.setShowSuggestions).toBeInstanceOf(Function);
    });

    it('should provide history state', () => {
      const { result } = renderHook(() => useHistoryState());

      expect(result.current.searchHistory).toBeDefined();
      expect(result.current.addToHistory).toBeInstanceOf(Function);
      expect(result.current.clearHistory).toBeInstanceOf(Function);
    });

    it('should provide performance state', () => {
      const { result } = renderHook(() => usePerformanceState());

      expect(result.current.performanceStats).toBeDefined();
      expect(result.current.startSearch).toBeInstanceOf(Function);
      expect(result.current.endSearch).toBeInstanceOf(Function);
    });

    it('should provide search config', () => {
      const { result } = renderHook(() => useSearchConfig());

      expect(result.current.config).toBeDefined();
      expect(result.current.updateConfig).toBeInstanceOf(Function);
      expect(result.current.resetConfig).toBeInstanceOf(Function);
    });
  });

  describe('Utility functions', () => {
    it('should generate unique search IDs', () => {
      const id1 = generateSearchId();
      const id2 = generateSearchId();

      expect(id1).toMatch(/^search_\d+_[a-z0-9]+$/);
      expect(id2).toMatch(/^search_\d+_[a-z0-9]+$/);
      expect(id1).not.toBe(id2);
    });

    it('should format performance stats', () => {
      const stats: SearchPerformanceStats = {
        averageSearchTime: 250,
        totalSearches: 100,
        failedSearches: 5,
        lastUpdated: Date.now(),
      };

      const formatted = formatPerformanceStats(stats);

      expect(formatted.averageTime).toBe('250ms');
      expect(formatted.totalSearches).toBe('100');
      expect(formatted.successRate).toBe('95.0%');
      expect(formatted.lastUpdated).toMatch(/\d+\/\d+\/\d+/);
    });

    it('should handle zero total searches in formatting', () => {
      const stats: SearchPerformanceStats = {
        averageSearchTime: 0,
        totalSearches: 0,
        failedSearches: 0,
        lastUpdated: Date.now(),
      };

      const formatted = formatPerformanceStats(stats);

      expect(formatted.successRate).toBe('0.0%');
    });
  });
});