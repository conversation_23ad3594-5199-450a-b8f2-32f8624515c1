/**
 * 主应用状态管理 Store
 * 
 * 使用 Zustand 管理整个应用的核心状态，包括：
 * - 搜索查询和结果
 * - 加载状态和错误处理
 * - UI 状态和用户偏好
 * - 过滤器配置
 */

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import type { SearchResultItem, SearchOptions } from '../models'
import type { SupportedLanguage } from '../i18n/I18nManager'

// ============================================================================
// 错误类型定义
// ============================================================================

export const ERROR_TYPES = {
  NETWORK: "network",
  SERVICE_INIT: "service_init",
  SEARCH: "search",
  DATABASE: "database",
  UNKNOWN: "unknown",
} as const

export type ErrorType = (typeof ERROR_TYPES)[keyof typeof ERROR_TYPES]

export interface ErrorInfo {
  type: ErrorType
  title: string
  message: string
  solutions: string[]
  canRetry: boolean
}

// ============================================================================
// 应用状态接口定义
// ============================================================================

export interface AppState {
  // 搜索相关状态
  searchQuery: string
  searchResults: SearchResultItem[]
  totalResults: number
  searchTime: number
  isLoading: boolean
  
  // 错误处理
  error: ErrorInfo | null
  
  // 过滤器和设置
  filters: Partial<SearchOptions>
  densityMode: 'compact' | 'comfortable'
  
  // UI 状态
  isInitialized: boolean
  i18nReady: boolean
  
  // i18n 状态
  currentLanguage: SupportedLanguage
  isLanguageChanging: boolean
}

// ============================================================================
// 应用操作接口定义
// ============================================================================

export interface AppActions {
  // 搜索操作
  setSearchQuery: (query: string) => void
  setSearchResults: (results: SearchResultItem[]) => void
  setSearchStats: (totalResults: number, searchTime: number) => void
  clearSearch: () => void
  
  // 加载状态
  setLoading: (loading: boolean) => void
  
  // 错误处理
  setError: (error: ErrorInfo | null) => void
  clearError: () => void
  
  // 过滤器操作
  setFilters: (filters: Partial<SearchOptions>) => void
  updateFilters: (partialFilters: Partial<SearchOptions>) => void
  clearFilters: () => void
  
  // UI 设置
  setDensityMode: (mode: 'compact' | 'comfortable') => void
  toggleDensityMode: () => void
  
  // 初始化状态
  setInitialized: (initialized: boolean) => void
  setI18nReady: (ready: boolean) => void
  
  // i18n 操作
  setCurrentLanguage: (language: SupportedLanguage) => void
  setLanguageChanging: (changing: boolean) => void
  
  // 批量状态更新
  batchUpdate: (updates: Partial<AppState>) => void
}

// ============================================================================
// Store 类型定义
// ============================================================================

export type AppStore = AppState & AppActions

// ============================================================================
// 初始状态
// ============================================================================

const getSafeDensityMode = (): 'compact' | 'comfortable' => {
  try {
    if (typeof localStorage !== 'undefined') {
      const stored = localStorage.getItem('recall-density-mode');
      if (stored === 'compact' || stored === 'comfortable') {
        return stored;
      }
    }
  } catch (error) {
    console.warn('Failed to access localStorage for density mode:', error);
  }
  return 'comfortable';
};

const initialState: AppState = {
  // 搜索相关状态
  searchQuery: "",
  searchResults: [],
  totalResults: 0,
  searchTime: 0,
  isLoading: false,
  
  // 错误处理
  error: null,
  
  // 过滤器和设置
  filters: {},
  densityMode: getSafeDensityMode(),
  
  // UI 状态
  isInitialized: false,
  i18nReady: false,
  
  // i18n 状态
  currentLanguage: 'en' as SupportedLanguage,
  isLanguageChanging: false,
}

// ============================================================================
// Zustand Store 创建
// ============================================================================

export const useAppStore = create<AppStore>()(
  subscribeWithSelector((set, get) => ({
    // 初始状态
    ...initialState,
    
    // ========================================================================
    // 搜索操作
    // ========================================================================
    
    setSearchQuery: (query: string) => {
      set({ searchQuery: query })
    },
    
    setSearchResults: (results: SearchResultItem[]) => {
      set({ 
        searchResults: results,
        totalResults: results.length 
      })
    },
    
    setSearchStats: (totalResults: number, searchTime: number) => {
      set({ totalResults, searchTime })
    },
    
    clearSearch: () => {
      set({
        searchQuery: "",
        searchResults: [],
        totalResults: 0,
        searchTime: 0,
        error: null,
      })
    },
    
    // ========================================================================
    // 加载状态管理
    // ========================================================================
    
    setLoading: (loading: boolean) => {
      set({ isLoading: loading })
    },
    
    // ========================================================================
    // 错误处理
    // ========================================================================
    
    setError: (error: ErrorInfo | null) => {
      set({ error, isLoading: false })
    },
    
    clearError: () => {
      set({ error: null })
    },
    
    // ========================================================================
    // 过滤器操作
    // ========================================================================
    
    setFilters: (filters: Partial<SearchOptions>) => {
      set({ filters })
    },
    
    updateFilters: (partialFilters: Partial<SearchOptions>) => {
      const currentFilters = get().filters
      set({ filters: { ...currentFilters, ...partialFilters } })
    },
    
    clearFilters: () => {
      set({ filters: {} })
    },
    
    // ========================================================================
    // UI 设置
    // ========================================================================
    
    setDensityMode: (mode: 'compact' | 'comfortable') => {
      set({ densityMode: mode })
      try {
        if (typeof localStorage !== 'undefined') {
          localStorage.setItem('recall-density-mode', mode)
        }
      } catch (error) {
        console.warn('Failed to save density mode to localStorage:', error);
      }
    },
    
    toggleDensityMode: () => {
      const currentMode = get().densityMode
      const newMode = currentMode === 'comfortable' ? 'compact' : 'comfortable'
      get().setDensityMode(newMode)
    },
    
    // ========================================================================
    // 初始化状态
    // ========================================================================
    
    setInitialized: (initialized: boolean) => {
      set({ isInitialized: initialized })
    },
    
    setI18nReady: (ready: boolean) => {
      set({ i18nReady: ready })
    },
    
    // ========================================================================
    // i18n 管理
    // ========================================================================
    
    setCurrentLanguage: (language: SupportedLanguage) => {
      set({ currentLanguage: language })
    },
    
    setLanguageChanging: (changing: boolean) => {
      set({ isLanguageChanging: changing })
    },
    
    // ========================================================================
    // 批量更新
    // ========================================================================
    
    batchUpdate: (updates: Partial<AppState>) => {
      set(updates)
    },
  }))
)

// ============================================================================
// 选择器 Hooks（性能优化）
// ============================================================================

/**
 * 搜索状态选择器
 */
export const useSearchState = () => useAppStore((state) => ({
  searchQuery: state.searchQuery,
  searchResults: state.searchResults,
  totalResults: state.totalResults,
  searchTime: state.searchTime,
  isLoading: state.isLoading,
  setSearchQuery: state.setSearchQuery,
  setSearchResults: state.setSearchResults,
  setSearchStats: state.setSearchStats,
  clearSearch: state.clearSearch,
  setLoading: state.setLoading,
}))

/**
 * 错误状态选择器
 */
export const useErrorState = () => useAppStore((state) => ({
  error: state.error,
  setError: state.setError,
  clearError: state.clearError,
}))

/**
 * 过滤器状态选择器
 */
export const useFiltersState = () => useAppStore((state) => ({
  filters: state.filters,
  setFilters: state.setFilters,
  updateFilters: state.updateFilters,
  clearFilters: state.clearFilters,
}))

/**
 * UI 状态选择器
 */
export const useUIState = () => useAppStore((state) => ({
  densityMode: state.densityMode,
  isInitialized: state.isInitialized,
  i18nReady: state.i18nReady,
  setDensityMode: state.setDensityMode,
  toggleDensityMode: state.toggleDensityMode,
  setInitialized: state.setInitialized,
  setI18nReady: state.setI18nReady,
}))

/**
 * i18n 状态选择器
 */
export const useI18nState = () => useAppStore((state) => ({
  currentLanguage: state.currentLanguage,
  isLanguageChanging: state.isLanguageChanging,
  i18nReady: state.i18nReady,
  setCurrentLanguage: state.setCurrentLanguage,
  setLanguageChanging: state.setLanguageChanging,
  setI18nReady: state.setI18nReady,
}))

/**
 * 只读状态选择器（用于显示组件）
 */
export const useStatusDisplay = () => useAppStore((state) => ({
  totalResults: state.totalResults,
  searchTime: state.searchTime,
  isLoading: state.isLoading,
  error: state.error,
}))

// ============================================================================
// 开发工具支持
// ============================================================================

// 在开发环境中启用调试功能
if (process.env.NODE_ENV === 'development') {
  // 可以在这里添加 Zustand DevTools 支持
  // 或者其他开发工具集成
  console.log('🏪 App Store initialized in development mode')
}