/**
 * Stores 统一导出
 * 
 * 集中管理所有 Zustand stores 的导出，便于在组件中使用
 */

// 主应用状态管理
export {
  useAppStore,
  useSearchState,
  useErrorState,
  useFiltersState,
  useUIState,
  useI18nState,
  useStatusDisplay,
  ERROR_TYPES,
  type AppState,
  type AppActions,
  type AppStore,
  type ErrorType,
  type ErrorInfo,
} from './appStore'

// 搜索功能状态管理
export {
  useSearchStore,
  useSuggestionsState,
  useHistoryState,
  usePerformanceState,
  useSearchConfig,
  generateSearchId,
  formatPerformanceStats,
  type SearchState,
  type SearchActions,
  type SearchStore,
  type SearchSuggestion,
  type SearchHistory,
  type SearchPerformanceStats,
  type SearchConfig,
} from './searchStore'

// Store 工具函数
export const resetAllStores = () => {
  // 可以在这里添加重置所有 store 的逻辑
  console.log('重置所有 stores')
}

// 开发工具
if (process.env.NODE_ENV === 'development') {
  // 在全局对象上暴露 stores 以便调试
  ;(window as any).__RECALL_STORES__ = {
    message: 'Stores available for debugging'
  }
}