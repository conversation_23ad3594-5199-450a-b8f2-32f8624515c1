/**
 * Chrome Storage 适配器
 * 
 * 为 Zustand persist 中间件提供 Chrome Extension 存储支持
 * 实现与 chrome.storage.local API 的集成
 */

import type { StateStorage } from 'zustand/middleware'

// ============================================================================
// Chrome Storage 适配器实现
// ============================================================================

/**
 * 创建 Chrome Storage 适配器
 * 
 * @param prefix - 存储键名前缀，用于避免与其他数据冲突
 * @returns Zustand StateStorage 接口实现
 */
export const createChromeStorageAdapter = (prefix: string = 'recall'): StateStorage => {
  return {
    getItem: async (name: string): Promise<string | null> => {
      try {
        // 检查 Chrome API 是否可用
        if (typeof chrome === 'undefined' || !chrome.storage) {
          console.warn('Chrome storage API not available, falling back to localStorage')
          return localStorage.getItem(`${prefix}_${name}`)
        }

        const key = `${prefix}_${name}`
        const result = await chrome.storage.local.get([key])
        
        return result[key] || null
      } catch (error) {
        console.error('Error reading from Chrome storage:', error)
        // 降级到 localStorage
        try {
          return localStorage.getItem(`${prefix}_${name}`)
        } catch (fallbackError) {
          console.error('Error reading from localStorage fallback:', fallbackError)
          return null
        }
      }
    },

    setItem: async (name: string, value: string): Promise<void> => {
      try {
        // 检查 Chrome API 是否可用
        if (typeof chrome === 'undefined' || !chrome.storage) {
          console.warn('Chrome storage API not available, falling back to localStorage')
          localStorage.setItem(`${prefix}_${name}`, value)
          return
        }

        const key = `${prefix}_${name}`
        await chrome.storage.local.set({ [key]: value })
      } catch (error) {
        console.error('Error writing to Chrome storage:', error)
        // 降级到 localStorage
        try {
          localStorage.setItem(`${prefix}_${name}`, value)
        } catch (fallbackError) {
          console.error('Error writing to localStorage fallback:', fallbackError)
          throw fallbackError
        }
      }
    },

    removeItem: async (name: string): Promise<void> => {
      try {
        // 检查 Chrome API 是否可用
        if (typeof chrome === 'undefined' || !chrome.storage) {
          console.warn('Chrome storage API not available, falling back to localStorage')
          localStorage.removeItem(`${prefix}_${name}`)
          return
        }

        const key = `${prefix}_${name}`
        await chrome.storage.local.remove([key])
      } catch (error) {
        console.error('Error removing from Chrome storage:', error)
        // 降级到 localStorage
        try {
          localStorage.removeItem(`${prefix}_${name}`)
        } catch (fallbackError) {
          console.error('Error removing from localStorage fallback:', fallbackError)
          throw fallbackError
        }
      }
    },
  }
}

// ============================================================================
// 预设适配器实例
// ============================================================================

/**
 * 默认的应用状态存储适配器
 */
export const appStorageAdapter = createChromeStorageAdapter('recall_app')

/**
 * 搜索状态存储适配器
 */
export const searchStorageAdapter = createChromeStorageAdapter('recall_search')

/**
 * 用户偏好设置存储适配器
 */
export const preferencesStorageAdapter = createChromeStorageAdapter('recall_prefs')

// ============================================================================
// 存储工具函数
// ============================================================================

/**
 * 获取 Chrome 存储使用情况
 */
export const getStorageUsage = async (): Promise<{
  bytesInUse: number
  quota: number
  percentage: number
}> => {
  try {
    if (typeof chrome === 'undefined' || !chrome.storage) {
      return { bytesInUse: 0, quota: 5242880, percentage: 0 } // 5MB 默认配额
    }

    const bytesInUse = await chrome.storage.local.getBytesInUse()
    const quota = chrome.storage.local.QUOTA_BYTES || 5242880 // 5MB
    const percentage = (bytesInUse / quota) * 100

    return { bytesInUse, quota, percentage }
  } catch (error) {
    console.error('Error getting storage usage:', error)
    return { bytesInUse: 0, quota: 5242880, percentage: 0 }
  }
}

/**
 * 清理所有 Recall 相关的存储数据
 */
export const clearAllRecallStorage = async (): Promise<void> => {
  try {
    if (typeof chrome === 'undefined' || !chrome.storage) {
      // 清理 localStorage 中的数据
      const keys = Object.keys(localStorage).filter(key => key.startsWith('recall_'))
      keys.forEach(key => localStorage.removeItem(key))
      return
    }

    // 获取所有存储的键
    const allStorage = await chrome.storage.local.get()
    const recallKeys = Object.keys(allStorage).filter(key => key.startsWith('recall_'))
    
    if (recallKeys.length > 0) {
      await chrome.storage.local.remove(recallKeys)
      console.log(`Cleared ${recallKeys.length} Recall storage items`)
    }
  } catch (error) {
    console.error('Error clearing Recall storage:', error)
    throw error
  }
}

/**
 * 导出所有 Recall 存储数据
 */
export const exportRecallStorage = async (): Promise<Record<string, any>> => {
  try {
    if (typeof chrome === 'undefined' || !chrome.storage) {
      // 从 localStorage 导出数据
      const data: Record<string, any> = {}
      const keys = Object.keys(localStorage).filter(key => key.startsWith('recall_'))
      keys.forEach(key => {
        const value = localStorage.getItem(key)
        if (value) {
          try {
            data[key] = JSON.parse(value)
          } catch {
            data[key] = value
          }
        }
      })
      return data
    }

    // 从 Chrome storage 导出数据
    const allStorage = await chrome.storage.local.get()
    const recallData: Record<string, any> = {}
    
    Object.keys(allStorage).forEach(key => {
      if (key.startsWith('recall_')) {
        recallData[key] = allStorage[key]
      }
    })

    return recallData
  } catch (error) {
    console.error('Error exporting Recall storage:', error)
    throw error
  }
}

/**
 * 导入 Recall 存储数据
 */
export const importRecallStorage = async (data: Record<string, any>): Promise<void> => {
  try {
    if (typeof chrome === 'undefined' || !chrome.storage) {
      // 导入到 localStorage
      Object.keys(data).forEach(key => {
        if (key.startsWith('recall_')) {
          const value = typeof data[key] === 'string' ? data[key] : JSON.stringify(data[key])
          localStorage.setItem(key, value)
        }
      })
      return
    }

    // 只导入 recall_ 开头的键
    const recallData: Record<string, any> = {}
    Object.keys(data).forEach(key => {
      if (key.startsWith('recall_')) {
        recallData[key] = data[key]
      }
    })

    if (Object.keys(recallData).length > 0) {
      await chrome.storage.local.set(recallData)
      console.log(`Imported ${Object.keys(recallData).length} Recall storage items`)
    }
  } catch (error) {
    console.error('Error importing Recall storage:', error)
    throw error
  }
}

// ============================================================================
// 存储监听器
// ============================================================================

/**
 * 监听 Chrome 存储变化
 */
export const addStorageChangeListener = (
  callback: (changes: Record<string, chrome.storage.StorageChange>) => void
): (() => void) => {
  if (typeof chrome === 'undefined' || !chrome.storage) {
    console.warn('Chrome storage API not available, storage change listener not added')
    return () => {} // 返回空的清理函数
  }

  const listener = (changes: Record<string, chrome.storage.StorageChange>, namespace: string) => {
    if (namespace === 'local') {
      // 只关注 recall_ 开头的变化
      const recallChanges: Record<string, chrome.storage.StorageChange> = {}
      Object.keys(changes).forEach(key => {
        if (key.startsWith('recall_')) {
          recallChanges[key] = changes[key]
        }
      })

      if (Object.keys(recallChanges).length > 0) {
        callback(recallChanges)
      }
    }
  }

  chrome.storage.onChanged.addListener(listener)

  // 返回清理函数
  return () => {
    if (chrome.storage && chrome.storage.onChanged) {
      chrome.storage.onChanged.removeListener(listener)
    }
  }
}

// ============================================================================
// 开发工具
// ============================================================================

if (process.env.NODE_ENV === 'development') {
  // 在开发环境中暴露存储工具到全局对象
  ;(window as any).__RECALL_STORAGE__ = {
    getStorageUsage,
    clearAllRecallStorage,
    exportRecallStorage,
    importRecallStorage,
    createChromeStorageAdapter,
  }
}