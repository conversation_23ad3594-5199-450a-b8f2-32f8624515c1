/**
 * 搜索状态管理 Store
 * 
 * 专门管理搜索功能的高级状态，包括：
 * - 搜索建议和自动完成
 * - 搜索历史记录
 * - 搜索性能统计
 * - 高级搜索配置
 */

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

// ============================================================================
// 搜索建议相关类型
// ============================================================================

export interface SearchSuggestion {
  query: string
  frequency: number
  lastUsed: number
  category?: 'recent' | 'popular' | 'suggestion'
}

export interface SearchHistory {
  query: string
  timestamp: number
  resultsCount: number
  searchTime: number
}

export interface SearchPerformanceStats {
  averageSearchTime: number
  totalSearches: number
  failedSearches: number
  lastUpdated: number
}

// ============================================================================
// 搜索配置接口
// ============================================================================

export interface SearchConfig {
  // 建议配置
  enableSuggestions: boolean
  maxSuggestions: number
  suggestionDelay: number
  
  // 搜索行为配置
  enableHistory: boolean
  maxHistoryItems: number
  autoSearch: boolean
  debounceDelay: number
  
  // 性能配置
  enablePerformanceTracking: boolean
  maxResultsPerPage: number
  enablePagination: boolean
}

// ============================================================================
// 搜索状态接口
// ============================================================================

export interface SearchState {
  // 建议相关状态
  suggestions: SearchSuggestion[]
  showSuggestions: boolean
  selectedSuggestionIndex: number
  isLoadingSuggestions: boolean
  
  // 搜索历史
  searchHistory: SearchHistory[]
  
  // 性能统计
  performanceStats: SearchPerformanceStats
  
  // 搜索配置
  config: SearchConfig
  
  // 当前搜索上下文
  currentSearchId: string | null
  searchStartTime: number | null
}

// ============================================================================
// 搜索操作接口
// ============================================================================

export interface SearchActions {
  // 建议操作
  setSuggestions: (suggestions: SearchSuggestion[]) => void
  addSuggestion: (suggestion: SearchSuggestion) => void
  clearSuggestions: () => void
  setShowSuggestions: (show: boolean) => void
  setSelectedSuggestionIndex: (index: number) => void
  setLoadingSuggestions: (loading: boolean) => void
  
  // 搜索历史操作
  addToHistory: (historyItem: SearchHistory) => void
  clearHistory: () => void
  removeFromHistory: (query: string) => void
  
  // 性能统计操作
  startSearch: (searchId: string) => void
  endSearch: (searchId: string, resultsCount: number, success: boolean) => void
  updatePerformanceStats: (stats: Partial<SearchPerformanceStats>) => void
  
  // 配置操作
  updateConfig: (config: Partial<SearchConfig>) => void
  resetConfig: () => void
  
  // 数据管理
  exportSearchData: () => { suggestions: SearchSuggestion[], history: SearchHistory[], stats: SearchPerformanceStats }
  importSearchData: (data: { suggestions?: SearchSuggestion[], history?: SearchHistory[], stats?: SearchPerformanceStats }) => void
  clearAllData: () => void
}

// ============================================================================
// Store 类型定义
// ============================================================================

export type SearchStore = SearchState & SearchActions

// ============================================================================
// 初始配置
// ============================================================================

const defaultConfig: SearchConfig = {
  // 建议配置
  enableSuggestions: true,
  maxSuggestions: 5,
  suggestionDelay: 300,
  
  // 搜索行为配置
  enableHistory: true,
  maxHistoryItems: 100,
  autoSearch: true,
  debounceDelay: 300,
  
  // 性能配置
  enablePerformanceTracking: true,
  maxResultsPerPage: 50,
  enablePagination: false,
}

const initialPerformanceStats: SearchPerformanceStats = {
  averageSearchTime: 0,
  totalSearches: 0,
  failedSearches: 0,
  lastUpdated: Date.now(),
}

// ============================================================================
// 初始状态
// ============================================================================

const initialState: SearchState = {
  // 建议相关状态
  suggestions: [],
  showSuggestions: false,
  selectedSuggestionIndex: -1,
  isLoadingSuggestions: false,
  
  // 搜索历史
  searchHistory: [],
  
  // 性能统计
  performanceStats: initialPerformanceStats,
  
  // 搜索配置
  config: defaultConfig,
  
  // 当前搜索上下文
  currentSearchId: null,
  searchStartTime: null,
}

// ============================================================================
// Zustand Store 创建
// ============================================================================

export const useSearchStore = create<SearchStore>()(
  subscribeWithSelector((set, get) => ({
    // 初始状态
    ...initialState,
    
    // ========================================================================
    // 建议操作
    // ========================================================================
    
    setSuggestions: (suggestions: SearchSuggestion[]) => {
      const { maxSuggestions } = get().config
      const limitedSuggestions = suggestions.slice(0, maxSuggestions)
      set({ 
        suggestions: limitedSuggestions,
        selectedSuggestionIndex: -1 
      })
    },
    
    addSuggestion: (suggestion: SearchSuggestion) => {
      const { suggestions, config } = get()
      const existingIndex = suggestions.findIndex(s => s.query === suggestion.query)
      
      let updatedSuggestions: SearchSuggestion[]
      if (existingIndex >= 0) {
        // 更新现有建议
        updatedSuggestions = [...suggestions]
        updatedSuggestions[existingIndex] = {
          ...updatedSuggestions[existingIndex],
          frequency: updatedSuggestions[existingIndex].frequency + 1,
          lastUsed: Date.now(),
        }
      } else {
        // 添加新建议
        updatedSuggestions = [suggestion, ...suggestions]
      }
      
      // 限制建议数量
      const limitedSuggestions = updatedSuggestions.slice(0, config.maxSuggestions)
      set({ suggestions: limitedSuggestions })
    },
    
    clearSuggestions: () => {
      set({ 
        suggestions: [],
        showSuggestions: false,
        selectedSuggestionIndex: -1 
      })
    },
    
    setShowSuggestions: (show: boolean) => {
      set({ showSuggestions: show })
    },
    
    setSelectedSuggestionIndex: (index: number) => {
      set({ selectedSuggestionIndex: index })
    },
    
    setLoadingSuggestions: (loading: boolean) => {
      set({ isLoadingSuggestions: loading })
    },
    
    // ========================================================================
    // 搜索历史操作
    // ========================================================================
    
    addToHistory: (historyItem: SearchHistory) => {
      const { searchHistory, config } = get()
      
      if (!config.enableHistory) return
      
      // 检查是否已存在相同查询
      const filteredHistory = searchHistory.filter(item => item.query !== historyItem.query)
      
      // 添加到历史记录顶部
      const updatedHistory = [historyItem, ...filteredHistory]
      
      // 限制历史记录数量
      const limitedHistory = updatedHistory.slice(0, config.maxHistoryItems)
      
      set({ searchHistory: limitedHistory })
    },
    
    clearHistory: () => {
      set({ searchHistory: [] })
    },
    
    removeFromHistory: (query: string) => {
      const { searchHistory } = get()
      const filteredHistory = searchHistory.filter(item => item.query !== query)
      set({ searchHistory: filteredHistory })
    },
    
    // ========================================================================
    // 性能统计操作
    // ========================================================================
    
    startSearch: (searchId: string) => {
      set({ 
        currentSearchId: searchId,
        searchStartTime: Date.now() 
      })
    },
    
    endSearch: (searchId: string, _resultsCount: number, success: boolean) => {
      const { currentSearchId, searchStartTime, performanceStats, config } = get()
      
      if (!config.enablePerformanceTracking || currentSearchId !== searchId || !searchStartTime) {
        return
      }
      
      const searchTime = Date.now() - searchStartTime
      const { totalSearches, failedSearches, averageSearchTime } = performanceStats
      
      const newTotalSearches = totalSearches + 1
      const newFailedSearches = success ? failedSearches : failedSearches + 1
      const newAverageTime = ((averageSearchTime * totalSearches) + searchTime) / newTotalSearches
      
      set({
        performanceStats: {
          averageSearchTime: Math.round(newAverageTime),
          totalSearches: newTotalSearches,
          failedSearches: newFailedSearches,
          lastUpdated: Date.now(),
        },
        currentSearchId: null,
        searchStartTime: null,
      })
    },
    
    updatePerformanceStats: (stats: Partial<SearchPerformanceStats>) => {
      const { performanceStats } = get()
      set({
        performanceStats: {
          ...performanceStats,
          ...stats,
          lastUpdated: Date.now(),
        }
      })
    },
    
    // ========================================================================
    // 配置操作
    // ========================================================================
    
    updateConfig: (newConfig: Partial<SearchConfig>) => {
      const { config } = get()
      set({ config: { ...config, ...newConfig } })
    },
    
    resetConfig: () => {
      set({ config: defaultConfig })
    },
    
    // ========================================================================
    // 数据管理
    // ========================================================================
    
    exportSearchData: () => {
      const { suggestions, searchHistory, performanceStats } = get()
      return {
        suggestions,
        history: searchHistory,
        stats: performanceStats,
      }
    },
    
    importSearchData: (data) => {
      const updates: Partial<SearchState> = {}
      
      if (data.suggestions) {
        updates.suggestions = data.suggestions
      }
      
      if (data.history) {
        updates.searchHistory = data.history
      }
      
      if (data.stats) {
        updates.performanceStats = data.stats
      }
      
      set(updates)
    },
    
    clearAllData: () => {
      set({
        suggestions: [],
        searchHistory: [],
        performanceStats: initialPerformanceStats,
        showSuggestions: false,
        selectedSuggestionIndex: -1,
        currentSearchId: null,
        searchStartTime: null,
      })
    },
  }))
)

// ============================================================================
// 选择器 Hooks（性能优化）
// ============================================================================

/**
 * 搜索建议状态选择器
 */
export const useSuggestionsState = () => useSearchStore((state) => ({
  suggestions: state.suggestions,
  showSuggestions: state.showSuggestions,
  selectedSuggestionIndex: state.selectedSuggestionIndex,
  isLoadingSuggestions: state.isLoadingSuggestions,
  setSuggestions: state.setSuggestions,
  setShowSuggestions: state.setShowSuggestions,
  setSelectedSuggestionIndex: state.setSelectedSuggestionIndex,
  setLoadingSuggestions: state.setLoadingSuggestions,
}))

/**
 * 搜索历史状态选择器
 */
export const useHistoryState = () => useSearchStore((state) => ({
  searchHistory: state.searchHistory,
  addToHistory: state.addToHistory,
  clearHistory: state.clearHistory,
  removeFromHistory: state.removeFromHistory,
}))

/**
 * 搜索性能状态选择器
 */
export const usePerformanceState = () => useSearchStore((state) => ({
  performanceStats: state.performanceStats,
  startSearch: state.startSearch,
  endSearch: state.endSearch,
  updatePerformanceStats: state.updatePerformanceStats,
}))

/**
 * 搜索配置选择器
 */
export const useSearchConfig = () => useSearchStore((state) => ({
  config: state.config,
  updateConfig: state.updateConfig,
  resetConfig: state.resetConfig,
}))

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 生成搜索 ID
 */
export const generateSearchId = (): string => {
  return `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 格式化性能统计
 */
export const formatPerformanceStats = (stats: SearchPerformanceStats) => {
  const successRate = stats.totalSearches > 0 
    ? ((stats.totalSearches - stats.failedSearches) / stats.totalSearches * 100).toFixed(1)
    : '0.0'
  
  return {
    averageTime: `${stats.averageSearchTime}ms`,
    totalSearches: stats.totalSearches.toLocaleString(),
    successRate: `${successRate}%`,
    lastUpdated: new Date(stats.lastUpdated).toLocaleString(),
  }
}