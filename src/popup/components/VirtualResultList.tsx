/**
 * 虚拟滚动搜索结果列表组件
 * 
 * 基于虚拟滚动技术优化大量搜索结果的渲染性能
 * 支持流式加载、高亮显示、键盘导航等特性
 */

import React, { useMemo, useRef, useEffect, useState, useCallback } from 'react';
import type { SearchResultItem } from '../../models';
import { formatTimestamp } from '../../models';
import { parseQuery } from '../../services/query-parser';
import { Highlight, HighlightWithContext } from '../../components/Highlight';
import { useStreamingResults } from '../hooks/useStreamingResults';
import { useVirtualScrolling, type VirtualScrollItemProps } from '../hooks/useVirtualScrolling';
import { useHistoryState, usePerformanceState } from '../../stores';
import '../../components/Highlight.css';

/**
 * 虚拟滚动结果列表属性接口
 */
interface VirtualResultListProps {
  /** 搜索结果 */
  results: SearchResultItem[];
  /** 是否正在加载 */
  isLoading?: boolean;
  /** 结果点击回调 */
  onResultClick: (result: SearchResultItem) => void;
  /** 搜索查询（用于高亮） */
  query?: string;
  /** 容器高度，默认400px */
  containerHeight?: number;
  /** 结果项高度，默认120px */
  itemHeight?: number;
  /** 是否启用虚拟滚动，默认true */
  enableVirtualScrolling?: boolean;
  /** 是否启用流式加载，默认true */
  enableStreaming?: boolean;
  /** 流式加载批次大小，默认15 */
  streamingBatchSize?: number;
  /** 虚拟滚动缓冲区大小，默认5 */
  overscan?: number;
}

/**
 * 虚拟滚动结果项属性接口
 */
interface VirtualResultItemProps extends VirtualScrollItemProps {
  /** 结果项 */
  result: SearchResultItem;
  /** 点击回调 */
  onClick: (result: SearchResultItem) => void;
  /** 搜索查询 */
  query?: string;
}

/**
 * 虚拟滚动单个结果项组件
 */
const VirtualResultItem: React.FC<VirtualResultItemProps> = ({ 
  result, 
  onClick, 
  query, 
  index, 
  height,
  isScrolling 
}) => {
  const { page, score, highlights } = result;

  /**
   * 解析查询以获取高亮关键词
   */
  const parsedQuery = useMemo(() => {
    if (!query) return null;
    return parseQuery(query);
  }, [query]);

  /**
   * 获取所有高亮关键词
   */
  const highlightKeywords = useMemo(() => {
    if (!parsedQuery) return [];
    return [...parsedQuery.keywords, ...parsedQuery.exact];
  }, [parsedQuery]);

  /**
   * 处理点击事件
   */
  const handleClick = useCallback(() => {
    onClick(result);
  }, [onClick, result]);

  /**
   * 处理键盘事件
   */
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClick(result);
    }
  }, [onClick, result]);

  /**
   * 获取域名图标
   */
  const getDomainIcon = useCallback((domain: string) => {
    const iconMap: Record<string, string> = {
      'github.com': '🐙',
      'stackoverflow.com': '📚',
      'developer.mozilla.org': '🦎',
      'google.com': '🔍',
      'youtube.com': '📺',
      'twitter.com': '🐦',
      'facebook.com': '📘',
      'linkedin.com': '💼',
      'reddit.com': '🤖',
      'wikipedia.org': '📖'
    };
    return iconMap[domain] || '🌐';
  }, []);

  /**
   * 格式化相关性分数
   */
  const formatScore = useCallback((score: number) => {
    return Math.round((1 - score) * 100);
  }, []);

  /**
   * 渲染高亮内容（优化版本，避免滚动时重复计算）
   */
  const renderHighlights = useMemo(() => {
    if (isScrolling) {
      // 滚动时显示简化版本以提升性能
      return (
        <div className="result-content scrolling-placeholder">
          <span className="content-preview">
            {page.content.substring(0, 100)}...
          </span>
        </div>
      );
    }

    if (!highlights || highlights.length === 0) {
      return (
        <div className="result-content">
          <HighlightWithContext
            text={page.content}
            keywords={highlightKeywords}
            className="highlight-content"
            contextLength={75}
            maxLength={300}
          />
        </div>
      );
    }

    return (
      <div className="result-highlights">
        {highlights.slice(0, 2).map((highlight, highlightIndex) => {
          const cleanText = highlight.replace(/<[^>]*>/g, '');
          return (
            <div key={highlightIndex} className="result-highlight">
              <Highlight
                text={cleanText}
                keywords={highlightKeywords}
                className="highlight-content"
                maxLength={200}
              />
            </div>
          );
        })}
      </div>
    );
  }, [highlights, highlightKeywords, page.content, isScrolling]);

  return (
    <div
      className={`virtual-result-item ${isScrolling ? 'scrolling' : ''}`}
      style={{ 
        height: `${height}px`,
        minHeight: `${height}px`,
        maxHeight: `${height}px`
      }}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`搜索结果 ${index + 1}: ${page.title}`}
      data-index={index}
    >
      {/* 结果头部 */}
      <div className="result-header">
        <div className="result-title-section">
          <span className="domain-icon" role="img" aria-label={`来源: ${page.domain}`}>
            {getDomainIcon(page.domain)}
          </span>
          <h3 className="result-title" title={page.title}>
            <Highlight
              text={page.title}
              keywords={highlightKeywords}
              className="highlight-title"
              maxLength={100}
            />
          </h3>
          <div className="result-score" title={`相关性: ${formatScore(score)}%`}>
            {formatScore(score)}%
          </div>
        </div>

        <div className="result-url" title={page.url}>
          <Highlight
            text={page.url}
            keywords={highlightKeywords}
            className="highlight-url"
            maxLength={80}
          />
        </div>
      </div>

      {/* 结果内容 */}
      <div className="result-body">
        {renderHighlights}
      </div>

      {/* 结果元信息 */}
      <div className="result-meta">
        <div className="result-meta-left">
          <span className="visit-time" title="访问时间">
            📅 {formatTimestamp(page.visitTime)}
          </span>
          <span className="access-count" title="访问次数">
            👁️ {page.accessCount} 次
          </span>
          <span className="domain" title="网站域名">
            🌐 {page.domain}
          </span>
        </div>
        
        <div className="result-meta-right">
          <span className="content-length" title="内容长度">
            📄 {Math.round(page.content.length / 1000)}k 字符
          </span>
        </div>
      </div>
    </div>
  );
};

/**
 * 加载状态组件
 */
const LoadingState: React.FC = () => (
  <div className="loading-state">
    <div className="loading-spinner-large" />
    <p>正在搜索...</p>
  </div>
);

/**
 * 空状态组件
 */
const EmptyState: React.FC<{ query?: string }> = ({ query }) => (
  <div className="empty-state">
    <span className="empty-icon">🔍</span>
    <h3>未找到相关结果</h3>
    {query && (
      <p>没有找到包含 "<strong><Highlight text={query} keywords={[]} /></strong>" 的页面</p>
    )}
    <div className="empty-tips">
      <p>建议：</p>
      <ul>
        <li>检查拼写是否正确</li>
        <li>尝试使用不同的关键词</li>
        <li>使用更简短的搜索词</li>
        <li>调整过滤条件</li>
      </ul>
    </div>
  </div>
);

/**
 * 性能统计组件
 */
const PerformanceStats: React.FC<{
  totalResults: number;
  visibleResults: number;
  isVirtualScrolling: boolean;
}> = ({ totalResults, visibleResults, isVirtualScrolling }) => {
  if (!isVirtualScrolling || process.env.NODE_ENV !== 'development') {
    return null;
  }

  const memoryReduction = totalResults > 0 ? 
    ((totalResults - visibleResults) / totalResults * 100).toFixed(1) : '0.0';

  return (
    <div className="performance-stats" title="虚拟滚动性能统计">
      <span className="stats-icon">⚡</span>
      <span className="stats-text">
        渲染 {visibleResults}/{totalResults} 项 (节省 {memoryReduction}% 内存)
      </span>
    </div>
  );
};

/**
 * 虚拟滚动结果列表组件
 */
export const VirtualResultList: React.FC<VirtualResultListProps> = ({
  results,
  isLoading = false,
  onResultClick,
  query,
  containerHeight = 400,
  itemHeight = 120,
  enableVirtualScrolling = true,
  enableStreaming = true,
  streamingBatchSize = 15,
  overscan = 5
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Zustand state hooks for enhanced functionality
  const { addToHistory } = useHistoryState();
  const { updatePerformanceStats } = usePerformanceState();

  /**
   * 流式加载Hook
   */
  const {
    displayedResults,
    isStreaming,
    progress
  } = useStreamingResults({
    results,
    batchSize: streamingBatchSize,
    delay: 30, // 虚拟滚动时缩短延迟
    enabled: enableStreaming && !isLoading
  });

  /**
   * 最终显示的结果
   */
  const finalResults = enableStreaming && !isLoading ? displayedResults : results;

  /**
   * 增强的结果点击处理器，包含 Zustand 状态更新
   */
  const handleResultClick = useCallback((result: SearchResultItem) => {
    // 调用原始的点击处理器
    onResultClick(result);
    
    // 如果有查询词，添加到搜索历史
    if (query && query.trim()) {
      addToHistory({
        query: query.trim(),
        timestamp: Date.now(),
        resultsCount: results.length,
        searchTime: 0 // 这里不追踪时间，因为搜索已经完成
      });
    }

    // 更新性能统计信息（用户交互相关）
    if (enableVirtualScrolling && results.length > 50) {
      updatePerformanceStats({
        lastUpdated: Date.now()
      });
    }
  }, [onResultClick, query, results.length, addToHistory, enableVirtualScrolling, updatePerformanceStats]);

  /**
   * 虚拟滚动Hook
   */
  const {
    virtualState,
    onScroll,
    scrollToIndex
  } = useVirtualScrolling(finalResults, {
    containerHeight,
    itemHeight,
    overscan,
    scrollDebounce: 16
  });

  /**
   * 组件初始化
   */
  useEffect(() => {
    if (containerRef.current && !isInitialized) {
      setIsInitialized(true);
    }
  }, [isInitialized]);

  /**
   * 键盘导航支持
   */
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!enableVirtualScrolling || finalResults.length === 0) return;

    switch (e.key) {
      case 'ArrowUp':
        e.preventDefault();
        if (virtualState.startIndex > 0) {
          scrollToIndex(Math.max(0, virtualState.startIndex - 1));
        }
        break;
      case 'ArrowDown':
        e.preventDefault();
        if (virtualState.endIndex < finalResults.length - 1) {
          scrollToIndex(Math.min(finalResults.length - 1, virtualState.endIndex + 1));
        }
        break;
      case 'Home':
        e.preventDefault();
        scrollToIndex(0);
        break;
      case 'End':
        e.preventDefault();
        scrollToIndex(finalResults.length - 1);
        break;
    }
  }, [enableVirtualScrolling, finalResults.length, virtualState, scrollToIndex]);

  /**
   * 渲染虚拟滚动内容
   */
  const renderVirtualContent = () => {
    if (!enableVirtualScrolling) {
      // 非虚拟滚动模式
      return (
        <div className="results-list non-virtual">
          {finalResults.map((result, index) => (
            <VirtualResultItem
              key={`${result.page.id}-${index}`}
              result={result}
              onClick={handleResultClick}
              query={query}
              index={index}
              height={itemHeight}
            />
          ))}
        </div>
      );
    }

    return (
      <div
        className="virtual-scroll-container"
        style={{ height: `${containerHeight}px`, overflow: 'auto' }}
        onScroll={onScroll}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="listbox"
        aria-label="搜索结果列表"
        ref={containerRef}
      >
        {/* 总容器，用于撑开滚动条 */}
        <div 
          className="virtual-scroll-spacer"
          style={{ height: `${virtualState.totalHeight}px`, position: 'relative' }}
        >
          {/* 可见项目容器 */}
          <div
            className="virtual-scroll-items"
            style={{
              position: 'absolute',
              top: `${virtualState.offsetY}px`,
              left: 0,
              right: 0
            }}
          >
            {virtualState.visibleItems.map((result, relativeIndex) => {
              const absoluteIndex = virtualState.startIndex + relativeIndex;
              return (
                <VirtualResultItem
                  key={`${result.page.id}-${absoluteIndex}`}
                  result={result}
                  onClick={handleResultClick}
                  query={query}
                  index={absoluteIndex}
                  height={itemHeight}
                />
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  /**
   * 渲染主要内容
   */
  const renderContent = () => {
    if (isLoading) {
      return <LoadingState />;
    }

    if (results.length === 0) {
      return <EmptyState query={query} />;
    }

    return (
      <div className="virtual-results-container">
        {/* 结果统计和控制 */}
        <div className="results-summary">
          <div className="results-info">
            <p>
              找到 <strong>{results.length}</strong> 个相关结果
              {enableVirtualScrolling && results.length > 20 && (
                <span className="virtual-scroll-hint">
                  （虚拟滚动已启用）
                </span>
              )}
            </p>
            
            {/* 流式加载进度指示 */}
            {enableStreaming && isStreaming && (
              <div className="streaming-progress">
                <span className="streaming-indicator">正在加载中...</span>
                <div className="streaming-progress-bar">
                  <div 
                    className="streaming-progress-fill" 
                    style={{ width: `${progress}%` }}
                  />
                </div>
              </div>
            )}
          </div>

          {/* 性能统计 */}
          <PerformanceStats
            totalResults={finalResults.length}
            visibleResults={virtualState.visibleItems.length}
            isVirtualScrolling={enableVirtualScrolling}
          />
        </div>

        {/* 结果列表 */}
        {renderVirtualContent()}

        {/* 键盘提示 */}
        {enableVirtualScrolling && finalResults.length > 10 && (
          <div className="keyboard-hints">
            <span className="hint-text">
              使用 ↑↓ 导航，Home/End 快速跳转
            </span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`virtual-result-list ${enableVirtualScrolling ? 'virtual-enabled' : 'virtual-disabled'}`}>
      {renderContent()}
    </div>
  );
};