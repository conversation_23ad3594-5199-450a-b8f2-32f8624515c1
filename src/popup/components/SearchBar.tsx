/**
 * 搜索栏组件
 * 
 * 提供搜索输入、清除按钮和加载状态显示
 * 使用 Zustand 进行状态管理和搜索建议功能
 */

import React, { useEffect, useRef, useCallback } from 'react';
import { hybridSearchService } from '../../services';
import { useSuggestionsState, useSearchConfig } from '../../stores';
import { SyntaxHelpWithTrigger } from './SyntaxHelp';

/**
 * 搜索栏属性接口
 */
interface SearchBarProps {
  /** 搜索值 */
  value: string;
  /** 值变化回调 */
  onChange: (value: string) => void;
  /** 清除回调 */
  onClear: () => void;
  /** 是否正在加载 */
  isLoading?: boolean;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否自动聚焦 */
  autoFocus?: boolean;
  /** 防抖延迟（毫秒） */
  debounceDelay?: number;
}

/**
 * 搜索栏组件
 */
export const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  onClear,
  isLoading = false,
  placeholder = '搜索...',
  autoFocus = true,
  debounceDelay = 300
}) => {
  // Zustand 状态管理
  const {
    suggestions,
    showSuggestions,
    selectedSuggestionIndex,
    isLoadingSuggestions,
    setSuggestions,
    setShowSuggestions,
    setSelectedSuggestionIndex,
    setLoadingSuggestions,
  } = useSuggestionsState();

  const { config } = useSearchConfig();
  
  const inputRef = useRef<HTMLInputElement>(null);
  const debounceTimerRef = useRef<number | null>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const blurTimerRef = useRef<number | null>(null);
  const isInternalUpdateRef = useRef(false);

  // 自动聚焦
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // 清理所有计时器，防止内存泄漏
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = null;
      }
      if (blurTimerRef.current) {
        clearTimeout(blurTimerRef.current);
        blurTimerRef.current = null;
      }
    };
  }, []);

  /**
   * 防抖处理输入变化
   */
  const debouncedOnChange = useCallback((newValue: string) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
      debounceTimerRef.current = null;
    }

    debounceTimerRef.current = window.setTimeout(() => {
      isInternalUpdateRef.current = true;
      onChange(newValue);
      // 延迟重置标志，确保组件能够接收到外部状态更新
      setTimeout(() => {
        isInternalUpdateRef.current = false;
      }, 50);
      debounceTimerRef.current = null;
    }, debounceDelay);
  }, [onChange, debounceDelay]);

  /**
   * 获取搜索建议
   */
  const fetchSuggestions = useCallback(async (query: string) => {
    if (!config.enableSuggestions || query.length < 2) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    setLoadingSuggestions(true);

    try {
      const suggestionsResult = await hybridSearchService.getSuggestions(query, config.maxSuggestions);
      
      // 转换为 SearchSuggestion 格式
      const formattedSuggestions = suggestionsResult.map(suggestion => ({
        query: suggestion,
        frequency: 1,
        lastUsed: Date.now(),
        category: 'suggestion' as const,
      }));
      
      setSuggestions(formattedSuggestions);
      setShowSuggestions(formattedSuggestions.length > 0);
      setSelectedSuggestionIndex(-1);
    } catch (error) {
      console.error('Failed to fetch suggestions:', error);
      setSuggestions([]);
      setShowSuggestions(false);
    } finally {
      setLoadingSuggestions(false);
    }
  }, [config.enableSuggestions, config.maxSuggestions, setSuggestions, setShowSuggestions, setSelectedSuggestionIndex, setLoadingSuggestions]);

  /**
   * 处理输入变化
   */
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    // 直接更新输入框显示，不使用内部状态
    debouncedOnChange(newValue);
    fetchSuggestions(newValue);
  }, [debouncedOnChange, fetchSuggestions]);

  /**
   * 处理键盘事件
   */
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showSuggestions || suggestions.length === 0) {
      if (e.key === 'Escape') {
        onClear();
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(
          selectedSuggestionIndex < suggestions.length - 1 ? selectedSuggestionIndex + 1 : 0
        );
        break;

      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(
          selectedSuggestionIndex > 0 ? selectedSuggestionIndex - 1 : suggestions.length - 1
        );
        break;

      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0) {
          const selectedSuggestion = suggestions[selectedSuggestionIndex];
          onChange(selectedSuggestion.query);
          setShowSuggestions(false);
        }
        break;

      case 'Escape':
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
        break;
    }
  }, [showSuggestions, suggestions, selectedSuggestionIndex, onChange, onClear]);

  /**
   * 处理建议点击
   */
  const handleSuggestionClick = useCallback((suggestionQuery: string) => {
    onChange(suggestionQuery);
    setShowSuggestions(false);
    setSelectedSuggestionIndex(-1);
  }, [onChange, setShowSuggestions, setSelectedSuggestionIndex]);

  /**
   * 处理清除按钮点击
   */
  const handleClearClick = useCallback(() => {
    setSuggestions([]);
    setShowSuggestions(false);
    setSelectedSuggestionIndex(-1);
    onClear();
    
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [onClear, setSuggestions, setShowSuggestions, setSelectedSuggestionIndex]);

  /**
   * 处理输入框失焦
   */
  const handleBlur = useCallback(() => {
    // 清理之前的计时器
    if (blurTimerRef.current) {
      clearTimeout(blurTimerRef.current);
      blurTimerRef.current = null;
    }
    
    // 延迟隐藏建议，以便点击建议项能够正常工作
    blurTimerRef.current = window.setTimeout(() => {
      setShowSuggestions(false);
      setSelectedSuggestionIndex(-1);
      blurTimerRef.current = null;
    }, 150);
  }, []);

  /**
   * 处理输入框聚焦
   */
  const handleFocus = useCallback(() => {
    if (suggestions.length > 0) {
      setShowSuggestions(true);
    }
  }, [suggestions.length]);

  return (
    <div className="search-bar">
      <div className="search-input-container">
        {/* 搜索图标 */}
        <div className={`search-icon ${isLoading || isLoadingSuggestions ? 'loading' : ''}`}>
          {isLoading || isLoadingSuggestions ? (
            <div className="loading-spinner" />
          ) : (
            <span className="search-icon-symbol">🔍</span>
          )}
        </div>

        {/* 输入框 */}
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          onFocus={handleFocus}
          placeholder={placeholder}
          className={`search-input ${isLoading ? 'loading' : ''} ${value ? 'has-value' : ''}`}
          disabled={isLoading}
        />

        {/* 语法帮助按钮 */}
        <div className="search-help-container">
          <SyntaxHelpWithTrigger position="bottom" />
        </div>

        {/* 清除按钮 */}
        {value && (
          <button
            type="button"
            onClick={handleClearClick}
            className="clear-button"
            title="清除搜索"
          >
            ✕
          </button>
        )}
      </div>

      {/* 搜索建议 */}
      {showSuggestions && suggestions.length > 0 && (
        <div ref={suggestionsRef} className="suggestions-dropdown">
          {suggestions.map((suggestion, index) => (
            <div
              key={suggestion.query}
              className={`suggestion-item ${
                index === selectedSuggestionIndex ? 'selected' : ''
              }`}
              onClick={() => handleSuggestionClick(suggestion.query)}
              onMouseEnter={() => setSelectedSuggestionIndex(index)}
            >
              <span className="suggestion-icon">
                {suggestion.category === 'recent' ? '🕒' : 
                 suggestion.category === 'popular' ? '🔥' : '💡'}
              </span>
              <span className="suggestion-text">{suggestion.query}</span>
              {suggestion.frequency > 1 && (
                <span className="suggestion-frequency">×{suggestion.frequency}</span>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
