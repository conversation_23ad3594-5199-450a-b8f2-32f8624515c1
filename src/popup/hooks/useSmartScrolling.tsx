import { useMemo } from 'react';
import type { SearchResultItem } from '../../models';
import { useSearchConfig } from '../../stores';

/**
 * 智能滚动配置接口
 */
interface SmartScrollingConfig {
  /** 启用虚拟滚动的最小结果数量阈值 */
  virtualScrollThreshold?: number;
  /** 容器高度 */
  containerHeight?: number;
  /** 结果项高度 */
  itemHeight?: number;
  /** 是否强制启用虚拟滚动 */
  forceVirtual?: boolean;
  /** 是否强制禁用虚拟滚动 */
  forceNonVirtual?: boolean;
}

/**
 * 智能滚动决策结果
 */
interface SmartScrollingDecision {
  /** 是否应该使用虚拟滚动 */
  shouldUseVirtual: boolean;
  /** 决策原因 */
  reason: string;
  /** 性能预期 */
  performanceExpectation: {
    memoryReduction: number;
    renderingImprovement: number;
    scrollingSmoothenss: number;
  };
  /** 推荐的配置 */
  recommendedConfig: {
    containerHeight: number;
    itemHeight: number;
    overscan: number;
    streamingBatchSize: number;
  };
}

/**
 * 智能滚动Hook
 * 根据搜索结果数量、设备性能和用户偏好自动选择最佳的滚动方式
 * 
 * @param results - 搜索结果数组
 * @param config - 配置选项
 * @returns 滚动决策和推荐配置
 */
export function useSmartScrolling(
  results: SearchResultItem[],
  config: SmartScrollingConfig = {}
): SmartScrollingDecision {
  // 从 Zustand 获取搜索配置
  const { config: searchConfig } = useSearchConfig();
  
  const {
    virtualScrollThreshold = 50,
    containerHeight = 400,
    itemHeight = 120,
    forceVirtual = false,
    forceNonVirtual = false
  } = config;

  return useMemo(() => {
    const resultCount = results.length;

    // 强制模式检查
    if (forceVirtual && !forceNonVirtual) {
      return {
        shouldUseVirtual: true,
        reason: '用户强制启用虚拟滚动',
        performanceExpectation: {
          memoryReduction: Math.min(90, (resultCount - 10) / resultCount * 100),
          renderingImprovement: 85,
          scrollingSmoothenss: 90
        },
        recommendedConfig: {
          containerHeight,
          itemHeight,
          overscan: 5,
          streamingBatchSize: 15
        }
      };
    }

    if (forceNonVirtual || resultCount === 0) {
      return {
        shouldUseVirtual: false,
        reason: resultCount === 0 ? '无搜索结果' : '用户强制禁用虚拟滚动',
        performanceExpectation: {
          memoryReduction: 0,
          renderingImprovement: 0,
          scrollingSmoothenss: resultCount < 20 ? 95 : 60
        },
        recommendedConfig: {
          containerHeight,
          itemHeight,
          overscan: 0,
          streamingBatchSize: 10
        }
      };
    }

    // 设备性能检测
    const devicePerformance = getDevicePerformance();
    
    // 智能决策逻辑
    let shouldUseVirtual = false;
    let reason = '';
    let memoryReduction = 0;
    let renderingImprovement = 0;
    let scrollingSmoothenss = 95;

    if (resultCount >= virtualScrollThreshold) {
      shouldUseVirtual = true;
      reason = `结果数量 ${resultCount} 超过阈值 ${virtualScrollThreshold}，启用虚拟滚动优化性能`;
      memoryReduction = Math.min(95, (resultCount - virtualScrollThreshold) / resultCount * 100);
      renderingImprovement = Math.min(90, 50 + (resultCount - virtualScrollThreshold) / 10);
      scrollingSmoothenss = devicePerformance.isLowEnd ? 75 : 90;
    } else if (resultCount >= 20 && devicePerformance.isLowEnd) {
      shouldUseVirtual = true;
      reason = `低性能设备检测到 ${resultCount} 个结果，启用虚拟滚动减少内存使用`;
      memoryReduction = Math.min(80, (resultCount - 20) / resultCount * 100);
      renderingImprovement = 60;
      scrollingSmoothenss = 70;
    } else if (resultCount >= 100) {
      shouldUseVirtual = true;
      reason = `大量结果 ${resultCount} 个，强制启用虚拟滚动避免页面卡顿`;
      memoryReduction = 90;
      renderingImprovement = 85;
      scrollingSmoothenss = devicePerformance.isLowEnd ? 70 : 85;
    } else {
      reason = `结果数量 ${resultCount} 较少，使用普通滚动即可满足性能需求`;
      scrollingSmoothenss = resultCount < 10 ? 95 : 80;
    }

    // 根据决策调整配置，考虑用户偏好
    const recommendedConfig = {
      containerHeight,
      itemHeight,
      overscan: shouldUseVirtual ? (devicePerformance.isLowEnd ? 3 : 5) : 0,
      streamingBatchSize: Math.max(
        5,
        Math.min(
          searchConfig.maxResultsPerPage / 5, // 基于用户设置的每页结果数调整
          shouldUseVirtual ? 
            (devicePerformance.isLowEnd ? 10 : 15) : 
            (resultCount > 30 ? 15 : 10)
        )
      )
    };

    return {
      shouldUseVirtual,
      reason,
      performanceExpectation: {
        memoryReduction,
        renderingImprovement,
        scrollingSmoothenss
      },
      recommendedConfig
    };
  }, [
    results.length,
    virtualScrollThreshold,
    containerHeight,
    itemHeight,
    forceVirtual,
    forceNonVirtual,
    searchConfig.maxResultsPerPage
  ]);
}

/**
 * 设备性能信息接口
 */
interface DevicePerformance {
  /** 是否为低端设备 */
  isLowEnd: boolean;
  /** 内存大小（GB，估算值） */
  memoryGB: number;
  /** 硬件并发数 */
  hardwareConcurrency: number;
  /** 是否支持WebGPU */
  supportsWebGPU: boolean;
  /** 性能评分（0-100） */
  performanceScore: number;
}

/**
 * 检测设备性能
 * 基于可用的浏览器API进行设备性能评估
 */
function getDevicePerformance(): DevicePerformance {
  // 硬件并发数检测
  const hardwareConcurrency = navigator.hardwareConcurrency || 4;
  
  // 内存估算（基于经验值）
  let memoryGB = 4; // 默认4GB
  
  // 如果支持deviceMemory API
  if ('deviceMemory' in navigator) {
    memoryGB = (navigator as Navigator & { deviceMemory?: number }).deviceMemory || 4;
  } else {
    // 基于硬件并发数估算
    if (hardwareConcurrency >= 8) {
      memoryGB = 8;
    } else if (hardwareConcurrency >= 4) {
      memoryGB = 4;
    } else {
      memoryGB = 2;
    }
  }

  // WebGPU支持检测
  const supportsWebGPU = 'gpu' in navigator;
  
  // 网络连接类型检测（可选）
  const connection = (navigator as Navigator & { 
    connection?: { effectiveType?: string } 
  }).connection;
  const isSlowConnection = connection && 
    (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g');

  // 综合性能评分
  let performanceScore = 50; // 基础分数
  
  // 硬件并发数影响 (0-25分)
  performanceScore += Math.min(25, hardwareConcurrency * 3);
  
  // 内存影响 (0-20分)
  performanceScore += Math.min(20, memoryGB * 2.5);
  
  // WebGPU支持 (+10分)
  if (supportsWebGPU) {
    performanceScore += 10;
  }
  
  // 网络连接影响 (-10分)
  if (isSlowConnection) {
    performanceScore -= 10;
  }
  
  // 用户代理字符串检测移动设备 (-5分)
  if (/Mobile|Android|iPhone|iPad/.test(navigator.userAgent)) {
    performanceScore -= 5;
  }

  performanceScore = Math.max(0, Math.min(100, performanceScore));
  
  // 低端设备判断
  const isLowEnd = performanceScore < 40 || 
    (hardwareConcurrency <= 2 && memoryGB <= 2);

  return {
    isLowEnd,
    memoryGB,
    hardwareConcurrency,
    supportsWebGPU,
    performanceScore
  };
}

/**
 * 获取性能优化建议
 */
export function getPerformanceRecommendations(
  decision: SmartScrollingDecision,
  resultCount: number
): string[] {
  const recommendations: string[] = [];

  if (decision.shouldUseVirtual) {
    recommendations.push('✅ 虚拟滚动已启用，大幅减少内存使用');
    
    if (decision.performanceExpectation.memoryReduction > 80) {
      recommendations.push('⚡ 内存使用减少超过80%，滚动性能显著提升');
    }
    
    if (resultCount > 100) {
      recommendations.push('🚀 大量结果优化：仅渲染可见项目，避免页面卡顿');
    }
  } else {
    recommendations.push('📋 普通滚动模式：适合当前结果数量');
    
    if (resultCount > 30) {
      recommendations.push('💡 建议：如遇到滚动卡顿，可在设置中手动启用虚拟滚动');
    }
  }

  // 流式加载建议
  if (decision.recommendedConfig.streamingBatchSize > 10) {
    recommendations.push('🌊 流式加载已优化：分批显示结果，提升感知速度');
  }

  return recommendations;
}

/**
 * 获取虚拟滚动的性能统计信息
 */
export function getVirtualScrollingStats(
  totalResults: number,
  visibleResults: number,
  isEnabled: boolean
): {
  title: string;
  metrics: Array<{
    label: string;
    value: string | number;
    unit?: string;
    type: 'positive' | 'neutral' | 'negative';
  }>;
} {
  if (!isEnabled) {
    return {
      title: '普通滚动模式',
      metrics: [
        {
          label: '渲染项目',
          value: totalResults,
          unit: '个',
          type: 'neutral'
        },
        {
          label: '内存使用',
          value: '100',
          unit: '%',
          type: 'neutral'
        }
      ]
    };
  }

  const renderRatio = totalResults > 0 ? (visibleResults / totalResults) : 0;
  const memoryReduction = (1 - renderRatio) * 100;

  return {
    title: '虚拟滚动模式',
    metrics: [
      {
        label: '可见项目',
        value: visibleResults,
        unit: `/${totalResults}`,
        type: 'positive'
      },
      {
        label: '内存节省',
        value: memoryReduction.toFixed(1),
        unit: '%',
        type: 'positive'
      },
      {
        label: '渲染比例',
        value: (renderRatio * 100).toFixed(1),
        unit: '%',
        type: memoryReduction > 50 ? 'positive' : 'neutral'
      }
    ]
  };
}