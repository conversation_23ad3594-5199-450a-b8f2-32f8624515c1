/**
 * Minimal type definitions for @mozilla/readability
 * Provides fallback types for test environment
 */

export interface ReadabilityOptions {
  debug?: boolean;
  maxElemsToParse?: number;
  nbTopCandidates?: number;
  charThreshold?: number;
  classesToPreserve?: string[];
  keepClasses?: boolean;
  serializer?: (node: Node) => string;
}

export interface ReadabilityResult {
  title: string;
  content: string;
  textContent: string;
  length: number;
  excerpt: string;
  byline: string;
  dir: string;
  siteName: string;
  lang: string;
}

export interface ReadabilityConstructor {
  new (document: Document, options?: ReadabilityOptions): ReadabilityInstance;
  isProbablyReaderable(document: Document, options?: { 
    minContentLength?: number;
    minScore?: number;
    visibilityChecker?: (node: Element) => boolean;
  }): boolean;
}

export interface ReadabilityInstance {
  parse(): ReadabilityResult | null;
}

// Export as default for compatibility
export const Readability: ReadabilityConstructor = {} as any;