/**
 * Centralized logging utility
 * 
 * Provides consistent logging across the application with proper prefixes,
 * log levels, and debug mode support.
 */

export const LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  NONE: 4
} as const;

export type LogLevel = typeof LogLevel[keyof typeof LogLevel];

export interface LoggerConfig {
  level: LogLevel;
  prefix: string;
  enableTimestamp: boolean;
  enableStackTrace: boolean;
}

class Logger {
  private config: LoggerConfig;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: LogLevel.INFO,
      prefix: '[Recall]',
      enableTimestamp: true,
      enableStackTrace: false,
      ...config
    };
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.config.level;
  }

  private formatMessage(level: string, message: unknown[], error?: Error): string {
    const timestamp = this.config.enableTimestamp ? new Date().toISOString() : '';
    const prefix = `${timestamp} ${this.config.prefix}[${level}]`;

    // Safely serialize message objects to avoid circular reference issues
    const messageText = message.map(m => {
      if (m === null) return 'null';
      if (m === undefined) return 'undefined';
      
      if (typeof m === 'object') {
        try {
          // For Error objects, use their message and name safely
          if (m instanceof Error) {
            const name = m.name || 'Error';
            const message = m.message || 'Unknown error';
            return `${name}: ${message}`;
          }
          // For other objects, try JSON.stringify with safe replacer
          return JSON.stringify(m, this.safeJsonReplacer);
        } catch {
          // Fallback for objects that can't be stringified (circular refs, etc.)
          try {
            return `[Object: ${Object.prototype.toString.call(m)}]`;
          } catch {
            return '[Object: Unknown]';
          }
        }
      }
      
      // Safe string conversion
      try {
        return String(m);
      } catch {
        return '[Value: Cannot convert to string]';
      }
    }).join(' ');

    let result = `${prefix} ${messageText}`;

    // Safely append error stack if available
    if (error && this.config.enableStackTrace) {
      try {
        if (error.stack && typeof error.stack === 'string') {
          result += `\n${error.stack}`;
        }
      } catch {
        // Ignore stack trace errors
      }
    }

    return result;
  }

  /**
   * Safe JSON replacer to handle circular references and problematic values
   */
  private safeJsonReplacer = (_key: string, value: unknown): unknown => {
    // Handle circular references
    if (typeof value === 'object' && value !== null) {
      // Simple circular reference detection
      try {
        JSON.stringify(value);
      } catch {
        return '[Circular Reference]';
      }
    }
    
    // Handle functions
    if (typeof value === 'function') {
      return '[Function]';
    }
    
    // Handle undefined (JSON.stringify normally omits these)
    if (value === undefined) {
      return '[Undefined]';
    }
    
    return value;
  };

  debug(...message: unknown[]): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.log(this.formatMessage('DEBUG', message));
    }
  }

  info(...message: unknown[]): void {
    if (this.shouldLog(LogLevel.INFO)) {
      console.log(this.formatMessage('INFO', message));
    }
  }

  warn(...message: unknown[]): void {
    if (this.shouldLog(LogLevel.WARN)) {
      console.warn(this.formatMessage('WARN', message));
    }
  }

  error(...message: unknown[]): void;
  error(error: Error, ...message: unknown[]): void;
  error(errorOrMessage: Error | unknown, ...message: unknown[]): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      if (errorOrMessage instanceof Error) {
        // Safely handle error object - ensure we don't pass objects with circular refs
        const safeError = this.sanitizeError(errorOrMessage);
        const formattedMessage = this.formatMessage('ERROR', message, safeError);
        // Use spread to avoid passing complex objects directly to console.error
        console.error(formattedMessage);
      } else {
        const formattedMessage = this.formatMessage('ERROR', [errorOrMessage, ...message]);
        console.error(formattedMessage);
      }
    }
  }

  /**
   * Safely sanitize error objects to prevent circular references and undefined properties
   */
  private sanitizeError(error: Error): Error | undefined {
    if (!error) return undefined;
    
    try {
      // Create a safe error object with only serializable properties
      const safeError = new Error(error.message || 'Unknown error');
      safeError.name = error.name || 'Error';
      
      // Only include stack if it exists and is a string
      if (error.stack && typeof error.stack === 'string') {
        safeError.stack = error.stack;
      }
      
      return safeError;
    } catch {
      // If anything goes wrong, return a minimal safe error
      return new Error('Error occurred but could not be safely serialized');
    }
  }

  setLevel(level: LogLevel): void {
    this.config.level = level;
  }

  createChild(prefix: string): Logger {
    return new Logger({
      ...this.config,
      prefix: `${this.config.prefix}[${prefix}]`
    });
  }
}

// Default logger instance
export const logger = new Logger();

// Specialized loggers for different modules
export const dbLogger = logger.createChild('DB');
export const searchLogger = logger.createChild('Search');
export const contentLogger = logger.createChild('Content');
export const backgroundLogger = logger.createChild('Background');

// Development mode detection
const isDev = process.env.NODE_ENV === 'development';
if (isDev) {
  logger.setLevel(LogLevel.DEBUG);
}

export default logger;