import lunr from 'lunr';
import type { Page, SearchResultItem } from '../../models/db.model';
import { logger } from '../../utils/logger';
import { lunrIndexStorage } from './LunrIndexStorage';

export interface LunrDocument {
  id: string;
  title: string;
  url: string;
  content: string;
}

export interface LunrSearchOptions {
  limit?: number;
  fuzzy?: boolean;
  fields?: string[];
  exactPhrases?: string[];
  excludeTerms?: string[];
}

export class LunrSearchEngine {
  private index: any = null;  // Lunr Index
  private documents: Map<string, Page> = new Map();
  private indexBuilder: any = null;  // Lunr Builder
  private isBuilding = false;
  private jieba: any = null; // To hold the dynamically imported jieba module

  constructor() {
    this.initializeIndex();
  }

  /**
   * Asynchronously loads and initializes the jieba-wasm module.
   * Caches the module's `cut` function on success.
   */
  private async loadJieba(): Promise<void> {
    if (this.jieba !== null) return; // Already loaded or failed, no need to retry

    try {
      // Check if we're in a service worker environment
      // Service workers have limited support for dynamic imports and WASM
      const isServiceWorker = typeof self !== 'undefined' && 'ServiceWorkerGlobalScope' in self;
      
      if (isServiceWorker) {
        logger.info('Running in service worker environment, skipping jieba-wasm loading');
        this.jieba = false;
        return;
      }

      // Dynamically import the wasm module. The vite-plugin-wasm should handle this.
      const jiebaModule = await import('jieba-wasm');
      
      // The default export is the initializer function that loads the wasm binary.
      // Handle different jieba-wasm export patterns with proper typing
      const moduleAny = jiebaModule as any;
      
      if (typeof moduleAny === 'function') {
        await moduleAny();
      } else if (moduleAny.default && typeof moduleAny.default === 'function') {
        await moduleAny.default();
      } else if (moduleAny.cut && typeof moduleAny.cut === 'function') {
        // Already initialized, jieba module is ready
        logger.info('jieba-wasm already initialized');
      } else {
        logger.warn('Unable to initialize jieba-wasm, using default tokenizer');
        throw new Error('jieba-wasm initialization failed');
      }

      // Cache the 'cut' function for later use.
      // The module exports the cut function directly, no initialization needed
      this.jieba = {
        cut: moduleAny.cut
      };
      logger.info('jieba-wasm loaded and initialized successfully.');
    } catch (error) {
      logger.warn('Failed to load jieba-wasm. Chinese segmentation will be disabled.', error);
      this.jieba = false; // Mark as failed to prevent retries
    }
  }

  /**
   * Load index from storage if available
   */
  async loadIndex(): Promise<boolean> {
    try {
      const storedIndex = await lunrIndexStorage.loadIndex();

      if (storedIndex) {
        // Parse the serialized index
        const indexData = JSON.parse(storedIndex.indexData);
        this.index = lunr.Index.load(indexData);

        // Note: We don't have the documents here, they need to be loaded separately
        // This is a limitation - we might need to enhance LunrIndexStorage to store documents too
        logger.info(`Lunr index loaded from IndexedDB`);
        logger.warn('Index loaded but document mapping is empty - this may cause search errors until documents are rebuilt');
        return true;
      }
    } catch (error) {
      logger.error('Failed to load Lunr index from IndexedDB:', error);
    }
    return false;
  }

  /**
   * Check if the index and document mapping are consistent
   * This helps detect when the index contains references to documents not in the mapping
   */
  public checkIndexConsistency(): { isConsistent: boolean; issues: string[] } {
    const issues: string[] = [];

    if (!this.index) {
      issues.push('No index available');
      return { isConsistent: false, issues };
    }

    if (this.documents.size === 0) {
      issues.push('Document mapping is empty but index exists');
      return { isConsistent: false, issues };
    }

    // Note: We can't easily check if index contains documents not in mapping
    // without performing a search, but we can check basic consistency
    logger.info(`Index consistency check: ${this.documents.size} documents in mapping`);

    return { isConsistent: true, issues: [] };
  }

  /**
   * Initialize a new Lunr index builder
   */
  private initializeIndex(): void {
    this.indexBuilder = new lunr.Builder();
    
    // Configure index fields with boost values
    this.indexBuilder.field('title', { boost: 10 });
    this.indexBuilder.field('url', { boost: 5 });
    this.indexBuilder.field('content', { boost: 1 });
    
    // Set reference field
    this.indexBuilder.ref('id');
    
    // Configure pipeline (can be customized later)
    this.indexBuilder.pipeline.add(
      lunr.trimmer,
      lunr.stopWordFilter,
      lunr.stemmer
    );
    
    // Configure search time pipeline
    this.indexBuilder.searchPipeline.add(
      lunr.stemmer
    );
  }

  /**
   * Create or rebuild the index from an array of pages
   * Target: < 500ms for 1000 pages
   */
  async createIndex(pages: Page[]): Promise<void> {
    const startTime = performance.now();
    
    // Ensure jieba is loaded before we start indexing.
    await this.loadJieba();
    
    try {
      this.isBuilding = true;
      this.documents.clear();
      this.initializeIndex();

      if (!this.indexBuilder) {
        throw new Error('Index builder not initialized');
      }

      // Add documents to the index
      for (const page of pages) {
        if (!page.id || !page.url) continue;

        // Conditionally apply Chinese word segmentation if jieba loaded successfully.
        const title = (this.jieba && page.title) ? this.jieba.cut(page.title).join(' ') : (page.title || '');
        const content = (this.jieba && page.content) ? this.jieba.cut(page.content).join(' ') : (page.content || '');

        const doc: LunrDocument = {
          id: page.id,
          title: title,
          url: page.url,
          content: content
        };

        this.indexBuilder.add(doc);
        this.documents.set(page.id, page);
      }

      // Build the index
      this.index = this.indexBuilder.build();
      this.indexBuilder = null; // Free memory

      // Save the index to IndexedDB for persistence
      try {
        const serializedIndex = JSON.stringify(this.index);
        const documentIds = Array.from(this.documents.keys());
        await lunrIndexStorage.saveIndex(serializedIndex, documentIds);
        logger.info('Lunr index saved to IndexedDB');
      } catch (saveError) {
        logger.error('Failed to save Lunr index to IndexedDB:', saveError);
        // Continue anyway - index is still in memory
      }

      const elapsed = performance.now() - startTime;
      logger.info(`Lunr index created: ${pages.length} pages in ${elapsed.toFixed(2)}ms`);
      
      if (pages.length >= 1000 && elapsed > 500) {
        logger.warn(`Index creation exceeded target: ${elapsed.toFixed(2)}ms for ${pages.length} pages`);
      }
    } catch (error) {
      logger.error('Failed to create Lunr index:', error);
      throw error;
    } finally {
      this.isBuilding = false;
    }
  }

  /**
   * Add a single page to the index (incremental indexing)
   */
  async addToIndex(page: Page): Promise<void> {
    if (!page.id || !page.url) {
      logger.warn('Cannot index page without id or url');
      return;
    }

    try {
      // Store the document
      this.documents.set(page.id, page);

      // For incremental indexing, we need to rebuild the entire index
      // Lunr doesn't support incremental updates to a built index
      await this.rebuildIndex();
    } catch (error) {
      logger.error('Failed to add page to index:', error);
      throw error;
    }
  }

  /**
   * Update a page in the index
   */
  async updateInIndex(page: Page): Promise<void> {
    if (!page.id) {
      logger.warn('Cannot update page without id');
      return;
    }

    try {
      // Update the stored document
      this.documents.set(page.id, page);

      // Rebuild the index
      await this.rebuildIndex();
    } catch (error) {
      logger.error('Failed to update page in index:', error);
      throw error;
    }
  }

  /**
   * Remove a page from the index
   */
  async removeFromIndex(pageId: string): Promise<void> {
    try {
      // Remove from documents
      this.documents.delete(pageId);

      // Rebuild the index
      await this.rebuildIndex();
    } catch (error) {
      logger.error('Failed to remove page from index:', error);
      throw error;
    }
  }

  /**
   * Rebuild the entire index from stored documents
   */
  private async rebuildIndex(): Promise<void> {
    const pages = Array.from(this.documents.values());
    await this.createIndex(pages);
  }

  /**
   * Search the index with the given query
   */
  async search(query: string, options: LunrSearchOptions = {}): Promise<SearchResultItem[]> {
    if (!this.index) {
      logger.warn('Search attempted before index creation');
      return [];
    }

    const trimmedQuery = query.trim();
    if (!trimmedQuery) {
      return [];
    }

    const startTime = performance.now();
    const { limit = 20 } = options;

    try {
      const queryTerms = trimmedQuery.toLowerCase().split(/\s+/).filter(t => t.length > 0);
      const { exactPhrases = [], excludeTerms = [] } = options;

      // Perform the search using the query builder for more robust queries.
      const searchResults = this.index.query((q: lunr.Query) => {
        // Handle exact phrases - all terms in phrase must be present
        if (exactPhrases.length > 0) {
          for (const phrase of exactPhrases) {
            const phraseTerms = phrase.toLowerCase().split(/\s+/).filter(t => t.length > 0);
            // For exact phrases, require all terms to be present
            for (const term of phraseTerms) {
              q.term(term, { 
                presence: lunr.Query.presence.REQUIRED,
                boost: 20 // High boost for exact phrase terms
              });
            }
          }
        } else {
          // Regular OR search when no exact phrases
          for (const term of queryTerms) {
            // Boost exact matches to bring them higher in the results.
            q.term(term, { boost: 10 });
            // Add a wildcard search to allow for prefix matching (e.g., "chro" matching "chrome").
            // This makes the search more flexible.
            if (term.length > 1) {
              q.term(term, { wildcard: lunr.Query.wildcard.TRAILING, boost: 5 });
            }
          }
        }
        
        // Handle exclude terms - these must NOT be present
        for (const excludeTerm of excludeTerms) {
          q.term(excludeTerm.toLowerCase(), { 
            presence: lunr.Query.presence.PROHIBITED 
          });
        }
      });

      // Get max score for normalization
      const maxScore = searchResults.length > 0 ? searchResults[0].score : 1;

      // Convert Lunr results to SearchResultItem format
      let results: SearchResultItem[] = searchResults
        .map((result: any) => {
          const page = this.documents.get(result.ref);
          if (!page) {
            // Enhanced logging for debugging document mapping issues
            logger.warn(`Document ${result.ref} not found in documents map - this indicates index-document mapping inconsistency`, {
              documentId: result.ref,
              totalDocumentsInMap: this.documents.size,
              searchScore: result.score,
              availableDocumentIds: Array.from(this.documents.keys()).slice(0, 5), // First 5 IDs for reference
              indexHasResults: searchResults.length > 0,
              suggestion: 'Index may need to be rebuilt to ensure consistency'
            });
            return null;
          }

          const normalizedScore = maxScore > 0 ? result.score / maxScore : 0;
          const highlights = this.extractHighlights(page, result.matchData, query);
          
          const matchedFields: string[] = [];
          const matchedTerms = Object.keys(result.matchData.metadata);

          if (result.matchData.metadata) {
            for (const term of matchedTerms) {
              const termData = result.matchData.metadata[term];
              for (const field in termData) {
                if (!matchedFields.includes(field)) {
                  matchedFields.push(field);
                }
              }
            }
          }

          return {
            page,
            score: normalizedScore,
            highlights,
            matchedFields,
            metadata: {
              matchedTerms,
              lunrRef: result.ref,
              lunrScore: result.score
            }
          } as SearchResultItem;
        })
        .filter((result: any): result is SearchResultItem => result !== null);

      // If exact phrases were specified, verify they appear in the correct order
      if (exactPhrases.length > 0) {
        results = results.filter(result => {
          const pageText = `${result.page.title || ''} ${result.page.content || ''}`.toLowerCase();
          return exactPhrases.every(phrase => 
            pageText.includes(phrase.toLowerCase())
          );
        });
      }

      // Apply limit after filtering
      results = results.slice(0, limit);

      const endTime = performance.now();
      const elapsed = endTime - startTime;
      logger.debug(`Lunr search completed: ${results.length} results in ${elapsed.toFixed(2)}ms`);

      return results;
    } catch (error) {
      logger.error('Lunr search failed:', error);
      console.error('[LUNR DEBUG] Full error:', error);
      console.error('[LUNR DEBUG] Error stack:', (error as any).stack);
      return [];
    }
  }

  /**
   * Extract highlights from Lunr match data
   */
  private extractHighlights(page: Page, matchData: any, query: string): string[] {
    const highlights: string[] = [];
    const content = page.content || '';
    
    if (!matchData || !matchData.metadata) {
      // Fallback to simple snippet if no match data
      return [this.getSnippet(page, query)];
    }

    try {
      // Process each matched term
      for (const term in matchData.metadata) {
        const termData = matchData.metadata[term];
        
        // Process each field where the term was found
        for (const field in termData) {
          const positions = termData[field].position || [];
          
          // Get text from the appropriate field
          let fieldText = '';
          switch (field) {
            case 'title':
              fieldText = page.title || '';
              break;
            case 'url':
              fieldText = page.url || '';
              break;
            case 'content':
              fieldText = content;
              break;
          }
          
          if (!fieldText) continue;

          // Extract snippets for each position
          for (const position of positions) {
            const [start, length] = position;
            const snippetStart = Math.max(0, start - 50);
            const snippetEnd = Math.min(fieldText.length, start + length + 50);
            
            let snippet = fieldText.substring(snippetStart, snippetEnd);
            
            // Add ellipsis if needed
            if (snippetStart > 0) snippet = '...' + snippet;
            if (snippetEnd < fieldText.length) snippet = snippet + '...';
            
            // Highlight the matched term
            const matchedText = fieldText.substring(start, start + length);
            snippet = snippet.replace(matchedText, `<mark>${matchedText}</mark>`);
            
            if (snippet && !highlights.includes(snippet)) {
              highlights.push(snippet);
            }
          }
        }
      }
    } catch (error) {
      logger.warn('Failed to extract highlights from match data:', error);
    }

    // If no highlights extracted, fall back to simple snippet
    if (highlights.length === 0) {
      highlights.push(this.getSnippet(page, query));
    }

    // Limit to 3 highlights
    return highlights.slice(0, 3);
  }

  /**
   * Get a text snippet around the matched terms
   */
  private getSnippet(page: Page, query: string, maxLength: number = 200): string {
    const content = page.content || '';
    if (!content) return '';

    // Find the first occurrence of any query term
    const terms = query.toLowerCase().split(/\s+/);
    let bestIndex = -1;
    
    for (const term of terms) {
      const index = content.toLowerCase().indexOf(term);
      if (index !== -1 && (bestIndex === -1 || index < bestIndex)) {
        bestIndex = index;
      }
    }

    if (bestIndex === -1) {
      // No match found, return beginning of content
      return content.substring(0, maxLength) + (content.length > maxLength ? '...' : '');
    }

    // Extract snippet around the match
    const start = Math.max(0, bestIndex - maxLength / 2);
    const end = Math.min(content.length, bestIndex + maxLength / 2);
    
    let snippet = content.substring(start, end);
    
    // Add ellipsis if needed
    if (start > 0) snippet = '...' + snippet;
    if (end < content.length) snippet = snippet + '...';
    
    return snippet;
  }

  /**
   * Get index statistics
   */
  getStats(): { documentCount: number; isBuilding: boolean; hasIndex: boolean } {
    return {
      documentCount: this.documents.size,
      isBuilding: this.isBuilding,
      hasIndex: this.index !== null
    };
  }

  /**
   * Clear the index and all documents
   */
  clear(): void {
    this.index = null;
    this.documents.clear();
    this.indexBuilder = null;
    this.isBuilding = false;
    logger.info('Lunr index cleared');
  }

  /**
   * Export the index for persistence
   */
  exportIndex(): string | null {
    if (!this.index) return null;
    return JSON.stringify(this.index);
  }

  /**
   * Import a previously exported index.
   * This now rebuilds the index from documents to ensure indexing logic is current.
   */
  async importIndex(documents: Page[]): Promise<void> {
    try {
      logger.info(`Importing ${documents.length} documents and rebuilding index...`);
      // We ignore the pre-serialized index data from the import
      // and rebuild from the raw documents to ensure the latest
      // tokenization and segmentation logic is applied.
      await this.createIndex(documents);
      logger.info(`Successfully imported documents and rebuilt index.`);
    } catch (error) {
      logger.error('Failed to import and rebuild index:', error);
      throw error;
    }
  }

  /**
   * Save index to persistent storage
   */
  async saveIndexToStorage(): Promise<void> {
    if (!this.index) {
      logger.warn('No index to save');
      return;
    }

    try {
      const indexData = this.exportIndex();
      if (!indexData) {
        logger.warn('Failed to export index for storage');
        return;
      }

      const documentIds = Array.from(this.documents.keys());
      await lunrIndexStorage.saveIndex(indexData, documentIds);
      
      logger.info('Lunr index saved to persistent storage');
    } catch (error) {
      logger.error('Failed to save index to storage:', error);
      throw error;
    }
  }

  /**
   * Load index from persistent storage
   * @returns True if index was loaded successfully
   */
  async loadIndexFromStorage(): Promise<boolean> {
    try {
      const storedIndex = await lunrIndexStorage.loadIndex();
      if (!storedIndex) {
        logger.debug('No stored index found');
        return false;
      }

      // Import the index
      this.index = lunr.Index.load(JSON.parse(storedIndex.indexData));
      
      // Note: Documents need to be loaded separately from the database
      // This method just loads the index structure
      logger.info(`Lunr index loaded from storage: ${storedIndex.documentCount} documents`);
      
      return true;
    } catch (error) {
      logger.error('Failed to load index from storage:', error);
      return false;
    }
  }

  /**
   * Initialize index with persistence support
   * Attempts to load from storage first, rebuilds if necessary
   */
  async initializeWithPersistence(pages: Page[]): Promise<void> {
    try {
      // If no pages provided, create empty index
      if (!pages || pages.length === 0) {
        logger.info('No pages provided for index initialization, creating empty index');
        await this.createIndex([]);
        return;
      }

      // Try to load stored index
      const storedIndex = await lunrIndexStorage.loadIndex();

      if (storedIndex) {
        // Check if rebuild is needed
        const needsRebuild = await lunrIndexStorage.needsRebuild(storedIndex, pages);

        if (!needsRebuild) {
          // Load the stored index
          this.index = lunr.Index.load(JSON.parse(storedIndex.indexData));

          // Rebuild documents map from provided pages
          this.documents.clear();
          for (const page of pages) {
            if (page.id) {
              this.documents.set(page.id, page);
            }
          }

          // Validate index-document consistency
          const validationResult = this.validateIndexDocumentConsistency(storedIndex);
          if (!validationResult.isValid) {
            logger.warn('Index-document consistency validation failed, rebuilding index', validationResult);
            await this.createIndex(pages);
            await this.saveIndexToStorage();
            return;
          }

          logger.info('Lunr index loaded from storage successfully', {
            documentCount: this.documents.size,
            indexDocumentCount: storedIndex.documentCount
          });
          return;
        }
      }
      
      // Create new index and save it
      logger.info('Creating new Lunr index...');
      await this.createIndex(pages);
      await this.saveIndexToStorage();
      
    } catch (error) {
      logger.error('Failed to initialize with persistence:', error);
      // Fall back to creating new index
      await this.createIndex(pages);
    }
  }

  /**
   * Get persistence statistics
   */
  async getPersistenceStats(): Promise<any> {
    return await lunrIndexStorage.getIndexStats();
  }

  /**
   * Get internal documents map for debugging
   * @returns Map of document ID to Page
   */
  getDocuments(): Map<string, Page> {
    return this.documents;
  }

  /**
   * Validate consistency between loaded index and document mapping
   * @private
   */
  private validateIndexDocumentConsistency(storedIndex: any): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    try {
      // Check if we have both index and documents
      if (!this.index) {
        issues.push('Index is not loaded');
      }

      if (this.documents.size === 0) {
        issues.push('Document mapping is empty');
      }

      // Check document count consistency
      if (storedIndex.documentCount !== this.documents.size) {
        issues.push(`Document count mismatch: stored=${storedIndex.documentCount}, current=${this.documents.size}`);
      }

      // Check if all stored document IDs exist in current document mapping
      const storedIds = new Set<string>(storedIndex.documentIds || []);
      const currentIds = new Set<string>(this.documents.keys());

      const missingInCurrent = Array.from(storedIds).filter(id => !currentIds.has(id));
      const extraInCurrent = Array.from(currentIds).filter(id => !storedIds.has(id));

      if (missingInCurrent.length > 0) {
        issues.push(`Documents in index but missing from mapping: ${missingInCurrent.slice(0, 5).join(', ')}${missingInCurrent.length > 5 ? '...' : ''}`);
      }

      if (extraInCurrent.length > 0) {
        issues.push(`Documents in mapping but missing from index: ${extraInCurrent.slice(0, 5).join(', ')}${extraInCurrent.length > 5 ? '...' : ''}`);
      }

      const isValid = issues.length === 0;

      if (!isValid) {
        logger.debug('Index-document validation details', {
          storedDocumentCount: storedIndex.documentCount,
          currentDocumentCount: this.documents.size,
          storedIds: Array.from(storedIds).slice(0, 10),
          currentIds: Array.from(currentIds).slice(0, 10),
          missingInCurrentCount: missingInCurrent.length,
          extraInCurrentCount: extraInCurrent.length
        });
      }

      return { isValid, issues };

    } catch (error) {
      issues.push(`Validation error: ${error instanceof Error ? error.message : String(error)}`);
      return { isValid: false, issues };
    }
  }
}

// Export singleton instance
export const lunrSearchEngine = new LunrSearchEngine();