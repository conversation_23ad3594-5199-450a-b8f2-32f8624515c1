/**
 * Extension Debug Helper
 * 
 * Simple script to test basic extension functionality
 */

// Check if we're in a Chrome extension environment
const isExtensionContext = typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
console.log('Extension context detected:', isExtensionContext);

if (isExtensionContext) {
  console.log('Extension ID:', chrome.runtime.id);
  console.log('Chrome version:', navigator.userAgent.match(/Chrome\/(\d+)/)?.[1] || 'unknown');
}

// Test localStorage access
try {
  const testValue = localStorage.getItem('test');
  localStorage.setItem('test', 'working');
  localStorage.removeItem('test');
  console.log('localStorage access: OK');
} catch (error) {
  console.error('localStorage access failed:', error);
}

// Test IndexedDB access
try {
  const request = indexedDB.open('test-db', 1);
  request.onsuccess = () => {
    console.log('IndexedDB access: OK');
    request.result.close();
    indexedDB.deleteDatabase('test-db');
  };
  request.onerror = (error) => {
    console.error('IndexedDB access failed:', error);
  };
} catch (error) {
  console.error('IndexedDB not available:', error);
}

// Basic React error patterns to watch for
const reactErrorPatterns = [
  'Cannot read prop',
  'Cannot read properties',
  'Hook was called',
  'Invalid hook call',
  'Cannot update a component',
  'Maximum update depth'
];

// Monitor console for React errors
const originalError = console.error;
console.error = function(...args) {
  const errorMessage = args.join(' ');
  
  if (reactErrorPatterns.some(pattern => errorMessage.includes(pattern))) {
    console.log('🚨 Potential React error detected:', errorMessage);
  }
  
  originalError.apply(console, args);
};

console.log('🔍 Debug helper loaded. Monitoring for React errors...');